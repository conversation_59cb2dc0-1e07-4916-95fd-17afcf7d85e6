# Barcode Scanning Guide for EMP_BioID

## Overview
The EMP_BioID is designed to be scannable with standard barcode scanners. Here's everything you need to know about making it work with your ASN-ITOP-092 or similar scanners.

## EMP_BioID Format
- **Format**: `EMP` + 5-digit number (e.g., `EMP12345`)
- **Length**: 8 characters total
- **Character Set**: Alphanumeric (A-Z, 0-9)
- **Barcode Type**: Code128 (most compatible)

## Barcode Generation

### 1. Automatic Generation
Every employee automatically gets a unique EMP_BioID when created:
```php
// This happens automatically in Employee model
$employee = Employee::create([
    'EMP_FirstName' => 'John',
    'EMP_LastName' => 'Doe',
    'EMP_Status' => 'active',
]);
// EMP_BioID is auto-generated (e.g., EMP12345)
```

### 2. Manual Barcode Generation
You can generate barcodes in different formats:

```php
$employee = Employee::find(1);

// SVG format (for web display)
$svgBarcode = $employee->generateBarcode();

// PNG format (for download/print)
$pngBarcode = $employee->getBarcodeImage();

// HTML format (for simple display)
$htmlBarcode = $employee->getBarcodeHtml();
```

## Scanner Configuration

### ASN-ITOP-092 Scanner Setup
1. **Scan Mode**: Set to "Keyboard Wedge" mode
2. **Terminator**: Configure to send "Enter" key after scan
3. **Prefix/Suffix**: No prefix or suffix needed
4. **Code Types**: Enable Code128, Code39, and EAN/UPC

### Scanner Settings Commands
Most scanners can be configured by scanning special barcodes:

1. **Enable Code128**: Scan the Code128 enable barcode from your scanner manual
2. **Set Keyboard Wedge Mode**: Scan the keyboard wedge barcode
3. **Add Enter Suffix**: Scan the "Add CR/LF" barcode

## Web Interface Integration

### 1. Auto-Submit on Scan
The Vue.js interface automatically submits when a complete barcode is scanned:

```javascript
// Auto-submit when barcode length >= 6 characters
watch(employee_code, (newCode) => {
  if (newCode && newCode.length >= 6 && Emp_Type.value) {
    setTimeout(() => {
      if (employee_code.value === newCode) {
        submit()
      }
    }, 100)
  }
})
```

### 2. Manual Entry Support
Users can also manually type the EMP_BioID if the scanner isn't working:
- Focus on the input field
- Type the complete EMP_BioID (e.g., `EMP12345`)
- Press Enter or click Submit

## Testing Your Scanner

### Test Barcodes
Use these test EMP_BioIDs for testing:
- `EMP12345` - Test One User
- `EMP54321` - Demo Two Employee  
- `EMP11111` - Quick Three Test

### Testing Steps
1. **Print Test Barcodes**:
   ```bash
   # Visit these URLs to print test barcodes
   /barcode/1/print  # Replace 1 with actual employee ID
   ```

2. **Scanner Test**:
   - Select an action (Check In, Check Out, etc.)
   - Scan the printed barcode
   - Verify the EMP_BioID appears in the input field
   - Check that the form auto-submits

3. **Manual Test**:
   - Select an action
   - Manually type `EMP12345`
   - Press Enter or click Submit

## Troubleshooting

### Scanner Not Working
1. **Check Scanner Mode**: Ensure it's in "Keyboard Wedge" mode
2. **Test with Notepad**: Open Notepad and scan - you should see the EMP_BioID typed
3. **Check USB Connection**: Ensure scanner is properly connected
4. **Browser Focus**: Make sure the input field has focus

### Barcode Not Scanning
1. **Print Quality**: Ensure barcode is printed clearly
2. **Size**: Barcode should be at least 1 inch wide
3. **Contrast**: Black bars on white background work best
4. **Distance**: Hold scanner 2-6 inches from barcode

### Auto-Submit Issues
1. **Check JavaScript**: Ensure JavaScript is enabled
2. **Action Selection**: Make sure an action is selected first
3. **Input Focus**: The input field should be focused

## Barcode Printing

### 1. Individual Employee Barcodes
```php
// Generate printable barcode page
Route::get('/barcode/{employee}/print', [BarcodeController::class, 'print']);

// Download barcode as PNG
Route::get('/barcode/{employee}/download', [BarcodeController::class, 'download']);
```

### 2. Bulk Barcode Generation
Create a custom command for bulk printing:

```bash
php artisan make:command GenerateBulkBarcodes
```

### 3. Barcode Labels
For professional barcode labels:
- Use label paper (Avery 5160 or similar)
- Print at 300 DPI or higher
- Include employee name below barcode
- Use Code128 format for best compatibility

## Security Considerations

### 1. Barcode Uniqueness
- Each EMP_BioID is automatically unique
- System prevents duplicate generation
- Database constraints ensure integrity

### 2. Access Control
- No authentication required for scanning (by design)
- Attendance logs track all scans
- Employee status (active/inactive) is checked

### 3. Audit Trail
- All scans are logged with timestamp
- Duplicate prevention (5-minute window)
- Status tracking (success/error/duplicate)

## API Integration

### Scan Endpoint
```http
POST /scan
Content-Type: application/json

{
    "employee_code": "EMP12345",
    "Emp_Type": "check_in"
}
```

### Response
```json
{
    "success": true,
    "message": "John Doe - Check In recorded successfully at 8:30 AM."
}
```

## Best Practices

1. **Scanner Placement**: Mount scanner at comfortable height
2. **Lighting**: Ensure adequate lighting for scanning
3. **Backup Method**: Always allow manual entry as backup
4. **Regular Testing**: Test scanner functionality regularly
5. **User Training**: Train users on proper scanning technique

## Common Scanner Models

### Compatible Scanners
- ASN-ITOP-092 ✅
- Honeywell Voyager 1200g ✅
- Symbol LS2208 ✅
- Zebra DS2208 ✅
- Any USB HID keyboard wedge scanner ✅

### Configuration Files
Most scanners come with configuration barcodes. Key settings:
- **Interface**: USB HID Keyboard
- **Terminator**: CR (Carriage Return)
- **Symbologies**: Code128, Code39, EAN/UPC
- **Beep**: Enable for scan confirmation
