# Time Attendance System

A comprehensive barcode-based time attendance system built with Laravel 12, Inertia.js, PrimeVue, and SQL Server.

## Features

- ✅ **No User Login Required** - Direct barcode scanning interface
- ✅ **Barcode Scanning** - Compatible with standard barcode scanners (ASN-ITOP-092 and similar)
- ✅ **4 Action Types** - Check In, Check Out, Break In, Break Out
- ✅ **Employee Management** - Full CRUD operations for employees
- ✅ **Auto Barcode Generation** - Automatic EMP_BioID generation for new employees
- ✅ **Barcode Printing** - Generate and print Code128 barcodes
- ✅ **Duplicate Prevention** - Prevents duplicate entries within 5 minutes
- ✅ **Real-time Feedback** - Instant success/error messages
- ✅ **Attendance Logs** - View and filter attendance records
- ✅ **SQL Server Support** - Optimized for SQL Server database
- ✅ **Responsive Design** - Works on desktop and mobile devices

## Technology Stack

- **Backend**: Laravel 12
- **Frontend**: Vue.js 3 + Inertia.js
- **UI Components**: PrimeVue 4
- **Styling**: Tailwind CSS
- **Database**: SQL Server (with SQLite/MySQL alternatives)
- **Barcode Generation**: milon/barcode (Code128)

## Installation

### Prerequisites

- PHP 8.2 or higher
- Composer
- Node.js 18+ and npm
- SQL Server (or SQLite for development)

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd TimeAttendance
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install JavaScript dependencies**
   ```bash
   npm install
   ```

4. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Database Configuration**
   
   For SQL Server (recommended):
   ```env
   DB_CONNECTION=sqlsrv
   DB_HOST=127.0.0.1
   DB_PORT=1433
   DB_DATABASE=TimeAttendance
   DB_USERNAME=sa
   DB_PASSWORD=your_password_here
   ```

   For SQLite (development):
   ```env
   DB_CONNECTION=sqlite
   DB_DATABASE=database/database.sqlite
   ```

6. **Run Migrations and Seeders**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

7. **Build Assets**
   ```bash
   npm run build
   # or for development
   npm run dev
   ```

8. **Start the Application**
   ```bash
   php artisan serve
   ```

## Usage

### Barcode Scanning

1. Open the application in your browser
2. Select the desired action (Check In, Check Out, Break In, Break Out)
3. Scan the employee barcode or manually enter the EMP_BioID
4. The system will automatically submit and provide feedback

### Employee Management

- Navigate to `/employees` to manage employee records
- Add new employees with automatic barcode generation
- View, edit, or delete existing employees
- Generate and print employee barcodes

### Barcode Generation

- Each employee automatically gets a unique EMP_BioID (format: EMP12345)
- Barcodes use Code128 format for maximum compatibility
- Print individual barcodes or download as PNG files

## Database Schema

### eTimeAttendance Table
- `EMP_EmpID` (Primary Key)
- `EMP_EmpNo` (Employee Number)
- `EMP_BioID` (Barcode ID - Unique)
- `EMP_FirstName`, `EMP_LastName`, `EMP_MiddleName`
- `EMP_FullName`
- `EMP_Department`
- `EMP_Status` (active, inactive, suspended)

### attendance_logs Table
- `id` (Primary Key)
- `EMP_EmpID` (Foreign Key)
- `EMP_BioID` (Scanned Barcode)
- `action_type` (check_in, check_out, break_in, break_out)
- `logged_at` (Timestamp)
- `log_date`, `log_time` (Separated for easier querying)
- `status` (success, duplicate, error)
- `notes` (Optional)

## API Endpoints

### Attendance
- `GET /` - Scanning interface
- `POST /scan` - Record attendance

### Employees
- `GET /employees` - List employees
- `POST /employees` - Create employee
- `GET /employees/{id}` - View employee
- `PUT /employees/{id}` - Update employee
- `DELETE /employees/{id}` - Delete employee

### Barcodes
- `GET /barcode/{employee}` - View barcode
- `GET /barcode/{employee}/print` - Print barcode
- `GET /barcode/{employee}/download` - Download barcode PNG

### Attendance Logs
- `GET /attendance-logs` - View all logs
- `GET /attendance-logs/{employee}` - Employee-specific logs

## Testing

Run the test suite:
```bash
php artisan test
```

Tests cover:
- Employee model functionality
- Barcode generation
- Attendance logging
- Duplicate prevention
- API endpoints
- Database relationships

## Configuration

### Barcode Settings
Modify `config/barcode.php` to customize barcode generation settings.

### Duplicate Prevention
The system prevents duplicate entries within 5 minutes by default. Modify this in `AttendanceController::store()`.

### Action Validation
The system includes logical sequence validation (e.g., warning if checking out without checking in). Customize in `AttendanceController::validateActionSequence()`.

## Deployment

### Production Checklist
- [ ] Set `APP_ENV=production` in `.env`
- [ ] Configure SQL Server connection
- [ ] Run `php artisan config:cache`
- [ ] Run `php artisan route:cache`
- [ ] Run `php artisan view:cache`
- [ ] Set up proper file permissions
- [ ] Configure web server (Apache/Nginx)
- [ ] Set up SSL certificate
- [ ] Configure backup strategy

## Troubleshooting

### Common Issues

1. **Barcode Scanner Not Working**
   - Ensure scanner is configured for Code128
   - Check that scanner sends Enter key after scan
   - Verify scanner compatibility with web browsers

2. **Database Connection Issues**
   - Verify SQL Server is running
   - Check connection credentials
   - Ensure PHP SQL Server drivers are installed

3. **Barcode Generation Errors**
   - Verify milon/barcode package is installed
   - Check PHP GD extension is enabled

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please create an issue in the repository or contact the development team.
