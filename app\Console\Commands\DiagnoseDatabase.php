<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Employee;
use App\Models\AttendanceLog;

class DiagnoseDatabase extends Command
{
    protected $signature = 'diagnose:database';
    protected $description = 'Diagnose database connection and table issues';

    public function handle()
    {
        $this->info('🔍 Diagnosing Database Connection...');
        
        try {
            // Test basic connection
            $this->info('Testing database connection...');
            $connection = DB::connection();
            $this->info('✅ Database connection successful');
            
            // Get database name
            $dbName = $connection->getDatabaseName();
            $this->info("📊 Connected to database: {$dbName}");
            
            // Test table existence
            $this->info('Checking table existence...');
            
            $tables = DB::select("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'");
            $tableNames = array_map(fn($table) => $table->TABLE_NAME, $tables);
            
            $this->table(['Table Name'], array_map(fn($name) => [$name], $tableNames));
            
            // Test Employee model
            $this->info('Testing Employee model...');
            $employeeCount = Employee::count();
            $this->info("✅ Employee count: {$employeeCount}");
            
            // Test AttendanceLog model
            $this->info('Testing AttendanceLog model...');
            $logCount = AttendanceLog::count();
            $this->info("✅ AttendanceLog count: {$logCount}");
            
            // Test specific queries that might be causing issues
            $this->info('Testing specific queries...');
            
            // Test today's logs
            $todayLogs = AttendanceLog::whereDate('log_date', today())->count();
            $this->info("✅ Today's logs: {$todayLogs}");
            
            // Test employee with logs
            $employeeWithLogs = Employee::with('attendanceLogs')->first();
            if ($employeeWithLogs) {
                $this->info("✅ Employee with logs: {$employeeWithLogs->EMP_FullName} has {$employeeWithLogs->attendanceLogs->count()} logs");
            }
            
            // Test the specific query that might be failing
            $this->info('Testing duplicate prevention query...');
            $testEmployee = Employee::first();
            if ($testEmployee) {
                $recentLog = AttendanceLog::where('EMP_EmpID', $testEmployee->EMP_EmpID)
                    ->where('action_type', 'check_in')
                    ->where('logged_at', '>=', now()->subMinutes(5))
                    ->first();
                
                $this->info("✅ Duplicate prevention query successful");
            }
            
            $this->info('🎉 All database tests passed!');
            
        } catch (\Exception $e) {
            $this->error('❌ Database error occurred:');
            $this->error($e->getMessage());
            $this->error('Stack trace:');
            $this->error($e->getTraceAsString());
            
            // Additional SQL Server specific diagnostics
            if (str_contains($e->getMessage(), 'Invalid object name')) {
                $this->warn('🔧 SQL Server Object Name Issue Detected');
                $this->info('This might be caused by:');
                $this->info('1. Table name case sensitivity');
                $this->info('2. Schema prefix issues');
                $this->info('3. Database context problems');
                
                // Try to get more specific information
                try {
                    $currentDb = DB::select('SELECT DB_NAME() as current_db')[0]->current_db;
                    $this->info("Current database context: {$currentDb}");
                } catch (\Exception $e2) {
                    $this->error("Could not determine current database context: " . $e2->getMessage());
                }
            }
        }
    }
}
