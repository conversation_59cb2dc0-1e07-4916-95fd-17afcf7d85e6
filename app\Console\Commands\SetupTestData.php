<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\TestDataSeeder;
use App\Models\Employee;

class SetupTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:test-data {--fresh : Fresh setup (migrate fresh)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup test data for the Time Attendance System';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Setting up Time Attendance System test data...');

        if ($this->option('fresh')) {
            $this->warn('⚠️  This will reset your database!');
            if ($this->confirm('Are you sure you want to continue?')) {
                $this->call('migrate:fresh');
            } else {
                $this->info('Operation cancelled.');
                return;
            }
        }

        // Run migrations if needed
        $this->info('📋 Running migrations...');
        $this->call('migrate');

        // Run seeders
        $this->info('🌱 Seeding database...');
        $this->call('db:seed');

        // Run test data seeder
        $this->info('🧪 Creating additional test data...');
        $this->call('db:seed', ['--class' => TestDataSeeder::class]);

        // Display summary
        $this->displaySummary();

        $this->info('✅ Test data setup complete!');
        $this->info('🌐 You can now access the application at: http://localhost:8000');
    }

    /**
     * Display a summary of created data
     */
    private function displaySummary()
    {
        $this->newLine();
        $this->info('📊 SUMMARY');
        $this->info('═══════════════════════════════════════');

        $totalEmployees = Employee::count();
        $activeEmployees = Employee::where('EMP_Status', 'active')->count();
        $testEmployees = Employee::where('EMP_Department', 'Testing')->count();

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Employees', $totalEmployees],
                ['Active Employees', $activeEmployees],
                ['Test Employees', $testEmployees],
            ]
        );

        $this->newLine();
        $this->info('🎯 TEST BARCODES FOR SCANNING:');
        $this->info('═══════════════════════════════════════');

        $testEmployees = Employee::where('EMP_Department', 'Testing')->get();
        foreach ($testEmployees as $employee) {
            $this->line("  📱 {$employee->EMP_BioID} - {$employee->EMP_FullName}");
        }

        $this->newLine();
        $this->info('🔗 USEFUL URLS:');
        $this->info('═══════════════════════════════════════');
        $this->line('  🏠 Main Scanner: http://localhost:8000');
        $this->line('  👥 Employee List: http://localhost:8000/employees');
        $this->line('  📊 Attendance Logs: http://localhost:8000/attendance-logs');

        if ($testEmployees->isNotEmpty()) {
            $firstEmployee = $testEmployees->first();
            $this->line("  🏷️  Test Barcode: http://localhost:8000/barcode/{$firstEmployee->EMP_EmpID}");
        }

        $this->newLine();
        $this->info('💡 QUICK START:');
        $this->info('═══════════════════════════════════════');
        $this->line('  1. Visit http://localhost:8000');
        $this->line('  2. Select an action (Check In, Check Out, etc.)');
        $this->line('  3. Scan or type: EMP12345');
        $this->line('  4. See the magic happen! ✨');
    }
}
