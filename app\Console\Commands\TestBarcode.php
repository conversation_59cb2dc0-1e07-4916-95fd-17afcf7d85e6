<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Employee;
use Milon\Barcode\Facades\DNS1DFacade as DNS1D;

class TestBarcode extends Command
{
    protected $signature = 'test:barcode {EMP_EmpID?}';
    protected $description = 'Test barcode generation for debugging';

    public function handle()
    {
        $employeeId = $this->argument('EMP_EmpID') ?? 1;
        
        $employee = Employee::find($employeeId);
        
        if (!$employee) {
            $this->error("Employee with ID {$employeeId} not found");
            return;
        }

        $this->info("Testing barcode generation for: {$employee->EMP_FullName}");
        $this->info("BioID: {$employee->EMP_BioID}");

        // Test 1: Direct DNS1D call
        $this->info("\n--- Test 1: Direct DNS1D call ---");
        try {
            $barcode = DNS1D::getBarcodeSVG($employee->EMP_BioID, 'CODE128', 2, 100);
            $this->info("✅ Direct DNS1D call successful");
            $this->info("SVG length: " . strlen($barcode));
        } catch (\Exception $e) {
            $this->error("❌ Direct DNS1D call failed: " . $e->getMessage());
        }

        // Test 2: Clean BioID
        $this->info("\n--- Test 2: Clean BioID ---");
        $cleanBioId = preg_replace('/[^A-Za-z0-9]/', '', $employee->EMP_BioID);
        $this->info("Clean BioID: {$cleanBioId}");
        
        try {
            $barcode = DNS1D::getBarcodeSVG($cleanBioId, 'CODE128', 2, 100);
            $this->info("✅ Clean BioID call successful");
            $this->info("SVG length: " . strlen($barcode));
        } catch (\Exception $e) {
            $this->error("❌ Clean BioID call failed: " . $e->getMessage());
        }

        // Test 3: Simple test string
        $this->info("\n--- Test 3: Simple test string ---");
        try {
            $barcode = DNS1D::getBarcodeSVG('TEST123', 'CODE128', 2, 100);
            $this->info("✅ Simple test string successful");
            $this->info("SVG length: " . strlen($barcode));
        } catch (\Exception $e) {
            $this->error("❌ Simple test string failed: " . $e->getMessage());
        }

        // Test 4: Employee model method
        $this->info("\n--- Test 4: Employee model method ---");
        try {
            $barcode = $employee->generateBarcode();
            $this->info("✅ Employee model method successful");
            $this->info("SVG length: " . strlen($barcode));
            
            // Check if it's our fallback SVG
            if (strpos($barcode, 'Barcode Error') !== false) {
                $this->warn("⚠️  Returned fallback SVG (error occurred)");
            }
        } catch (\Exception $e) {
            $this->error("❌ Employee model method failed: " . $e->getMessage());
        }

        // Test 5: Different barcode types
        $this->info("\n--- Test 5: Different barcode types ---");
        $types = ['CODE128', 'CODE39', 'CODE93'];
        
        foreach ($types as $type) {
            try {
                $barcode = DNS1D::getBarcodeSVG($cleanBioId, $type, 2, 100);
                $this->info("✅ {$type}: successful (length: " . strlen($barcode) . ")");
            } catch (\Exception $e) {
                $this->error("❌ {$type}: failed - " . $e->getMessage());
            }
        }

        // Test 6: Check barcode library installation
        $this->info("\n--- Test 6: Library check ---");
        $this->info("DNS1D class exists: " . (class_exists('Milon\Barcode\DNS1D') ? 'Yes' : 'No'));
        $this->info("Facade exists: " . (class_exists('Milon\Barcode\Facades\DNS1DFacade') ? 'Yes' : 'No'));
        
        // Check if GD extension is loaded
        $this->info("GD extension loaded: " . (extension_loaded('gd') ? 'Yes' : 'No'));
    }
}
