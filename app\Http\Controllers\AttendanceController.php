<?php

namespace App\Http\Controllers;

use App\Models\ActionType;
use App\Models\AttendanceLog;
use App\Models\Employee;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AttendanceController extends Controller
{
    public function index()
    {
        return Inertia::render('Scan');
    }

    /**
     * Lookup employee details by BioID
     */

    public function lookupEmployee($bioId) {
        $employee = Employee::where('EMP_BioID', $bioId)->first();

        if (!$employee)  {
            return response()->json([
                'success' => false,
                'message' => 'Employee not found'
            ], 404);
        }

        // Check if employee is active (EMP_IsActive=1)
        if (!$employee->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Employee is inactive and cannot perform attendance actions'
            ], 403);
        }

        $lastAction = $employee->lastAttendanceAction();
        $currentState = $lastAction ? $lastAction->action_type : null; // Using numeric action types: 1=check_in, 2=check_out, 3=break_in, 4=break_out

        $allowedAction = ['check_in', 'check_out', 'break_in', 'break_out'];

        return response()->json([
            'success' => true,
            'employee' => [
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_BioID' => $employee->EMP_BioID,
                'EMP_EmpNo' => $employee->EMP_EmpNo,
                'EMP_FullName' => $employee->EMP_FullName,
                'EMP_FirstName' => $employee->EMP_FirstName,
                'EMP_LastName' => $employee->EMP_LastName,
                'EMP_Department' => $employee->EMP_Department,
                'EMP_IsActive' => $employee->EMP_IsActive,
                // 'EMP_Status' => $employee->status,
                'EMP_Photo' => $employee->photo_url,
                'last_action' => $lastAction ? [
                    'action_type' => $lastAction->action_type,
                    'logged_at' => $lastAction->logged_at->format('M d, Y g:i A'),
                    'action_label' => $lastAction->action_label
                ] : null,
                'is_checked_in' => $employee->isCheckedIn(),
                'current_state' => $currentState,
                'allowed_actions' => $allowedAction
            ]
        ]);
    }

    public function store(Request $request) {
        // Debug logging
        \Log::info('AttendanceController::store called', [
            'request_data' => $request->all(),
            'method' => $request->method(),
            'url' => $request->url(),
        ]);

        $request->validate ([
            'employee_code' => 'required|string|min:6',
            'Emp_Type' => 'required|in:check_in,check_out,break_in,break_out',
        ]);

        $employee = Employee::where('EMP_BioID', $request->employee_code)->first();

        if(!$employee){
             return redirect()->back()->with('error', 'Employee not found with barcode: ' . $request->employee_code);
        }

        // Check if employee is active (EMP_IsActive=1)
        if (!$employee->isActive()) {
            return redirect()->back()->with('error', 'Employee is inactive and cannot perform attendance actions.');
        }

        // Map action types to numeric IDs (1=check_in, 2=check_out, 3=break_in, 4=break_out)
        $actionTypeMap = [
            'check_in' => 1,
            'check_out' => 2,
            'break_in' => 3,
            'break_out' => 4,
        ];

        $actionTypeId = $actionTypeMap[$request->Emp_Type] ?? null;

        if (!$actionTypeId) {
            return redirect()->back()->with('error', 'Invalid action type: ' . $request->Emp_Type);
        }

        \Log::info('Creating attendance log', [
            'employee_id' => $employee->EMP_EmpID,
            'bio_id' => $employee->EMP_BioID,
            'action_type_id' => $actionTypeId,
            'action_type_string' => $request->Emp_Type,
        ]);

        try {
            $log = AttendanceLog::create([
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_BioID' => $employee->EMP_BioID,
                'action_type' => $actionTypeId,
                'logged_at' => now(),
            ]);

            \Log::info('Attendance log created successfully', [
                'log_id' => $log->id,
                'action_type' => $log->action_type,
                'logged_at' => $log->logged_at,
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to create attendance log', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return redirect()->back()->with('error', 'Failed to record attendance: ' . $e->getMessage());
        }

        // Map numeric action types back to labels
        $actionLabels = [
            1 => 'Check In',
            2 => 'Check Out',
            3 => 'Break In',
            4 => 'Break Out',
        ];

        $actionLabel = $actionLabels[$actionTypeId] ?? 'Unknown Action';
        $timeFormatted = $log->logged_at->format('g:i A');

        return redirect()->back()->with('success', "{$employee->EMP_FullName} - {$actionLabel} recorded successfully at {$timeFormatted}.");
    }


    /**
     * Get allowed actions - now returns all actions since there are no restrictions
     */
    private function getAllowedActions($currentState): array
    {
        // Return all possible actions - no restrictions
        return ['check_in', 'check_out', 'break_in', 'break_out'];
    }

    /**
     * Get action label for display
     */
    private function getActionLabel($actionType): string
    {
        return match($actionType) {
            'check_in' => 'Check In',
            'check_out' => 'Check Out',
            'break_in' => 'Break In',
            'break_out' => 'Break Out',
            default => ucfirst(str_replace('_', ' ', $actionType))
        };
    }

    /**
     * Display attendance logs
     */
    public function logs(Request $request)
    {
        $query = AttendanceLog::with('employee');

        // Filter by date
        if ($request->filled('date')) {
            $query->whereDate('log_date', $request->date);
        } else {
            // Default to today
            $query->whereDate('log_date', today());
        }

        // Filter by employee
        if ($request->filled('EMP_EmpID')) {
            $query->where('EMP_EmpID', $request->EMP_EmpID);
        }

        // Filter by action type (using numeric system)
        if ($request->filled('action_type')) {
            // If it's a numeric ID, use it directly
            if (is_numeric($request->action_type)) {
                $query->where('action_type', $request->action_type);
            } else {
                // If it's a string, convert to ID
                $actionType = ActionType::getByActionType($request->action_type);
                if ($actionType) {
                    $query->where('action_type', $actionType->Action_Type);
                }
            }
        }

        $logs = $query->with('actionType')
                     ->orderBy('logged_at', 'desc')
                     ->paginate(50)
                     ->withQueryString();

        $employees = Employee::where('EMP_IsActive', 1)
                            ->orderBy('EMP_FirstName')
                            ->get(['EMP_EmpID', 'EMP_FullName']);

        // Get action types from the database
        $actionTypes = ActionType::getAllForFrontend();

        return Inertia::render('Attendance/Logs', [
            'logs' => $logs,
            'employees' => $employees,
            'actionTypes' => $actionTypes,
            'filters' => $request->only(['date', 'EMP_EmpID', 'action_type']),
        ]);
    }

    /**
     * Display attendance logs for a specific employee
     */
    public function employeeLogs(Request $request, Employee $employee)
    {
        $query = $employee->attendanceLogs();

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('log_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('log_date', '<=', $request->end_date);
        }

        // Default to last 30 days if no date filter
        if (!$request->filled('start_date') && !$request->filled('end_date')) {
            $query->whereDate('log_date', '>=', now()->subDays(30));
        }

        $logs = $query->orderBy('logged_at', 'desc')
                     ->paginate(100)
                     ->withQueryString();

        return Inertia::render('Attendance/EmployeeLogs', [
            'employee' => $employee,
            'logs' => $logs,
            'filters' => $request->only(['start_date', 'end_date']),
        ]);
    }
}

