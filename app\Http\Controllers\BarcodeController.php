<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use Inertia\Inertia;

class BarcodeController extends Controller
{
    /**
     * Display barcode for an employee
     */
    public function show(Employee $employee)
    {
        return Inertia::render('Barcode/Show', [
            'employee' => $employee,
            'barcode' => $employee->generateBarcode(),
        ]);
    }

    /**
     * Display printable barcode page
     */
    public function print(Employee $employee)
    {
        return Inertia::render('Barcode/Print', [
            'employee' => $employee,
            'barcode' => $employee->generateBarcode('CODE128', 3, 120),
        ]);
    }

    /**
     * Download barcode as PNG
     */
    public function download(Employee $employee)
    {
        // For now, redirect to the SVG view since PNG generation is not working
        // In a production environment, you would implement proper PNG generation
        return redirect()->route('barcode.show', $employee)
            ->with('info', 'PNG download is not available. Please use the print function or right-click to save the barcode image.');
    }

    /**
     * Generate barcode SVG for API use
     */
    public function svg(Employee $employee)
    {
        $barcode = $employee->generateBarcode();
        
        return response($barcode)
            ->header('Content-Type', 'image/svg+xml');
    }
}
