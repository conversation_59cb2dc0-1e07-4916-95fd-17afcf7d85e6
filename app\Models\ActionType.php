<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ActionType extends Model
{
    protected $table = 'eActionType';
    public $timestamps = false; // Existing table doesn't have timestamps

    protected $fillable = [
        'Type',
        'Action_Type',
    ];

    /**
     * Get the attendance logs for this action type
     */
    public function attendanceLogs(): HasMany
    {
        return $this->hasMany(AttendanceLog::class, 'action_type', 'Action_Type');
    }

    /**
     * Get action type by string value
     */
    public static function getByActionType(string $actionType): ?self
    {
        return static::where('Type', $actionType)->first();
    }

    /**
     * Get all action types for frontend
     */
    public static function getAllForFrontend(): array
    {
        return static::all()->map(function ($actionType) {
            return [
                'action_type' => $actionType->Type,
                'action_label' => ucfirst(str_replace('_', ' ', $actionType->Type)),
            ];
        })->toArray();
    }
}
