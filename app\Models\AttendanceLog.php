<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class AttendanceLog extends Model
{
    protected $table = 'attendance_logs';
    public $timestamps = false; // Disable Laravel timestamps since table doesn't have created_at/updated_at
    public $incrementing = false; // Disable auto-incrementing since we'll generate IDs manually
    protected $keyType = 'int'; // Set key type as integer

    protected $fillable = [
        'id',
        'EMP_EmpID',
        'EMP_BioID',
        'action_type',
        'logged_at',
        'log_date',
        'log_time',
    ];

    protected $casts = [
        'logged_at' => 'datetime',
    ];

   
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            // Generate incremental ID if not set
            if (empty($log->id)) {
                $maxId = static::max('id') ?? 0;
                $log->id = $maxId + 1;
            }

            // Use current timestamp if logged_at is not provided
            if (empty($log->logged_at)) {
                $log->logged_at = now();
            }

            // Parse the logged_at timestamp to ensure all time fields are synchronized
            $loggedAt = Carbon::parse($log->logged_at);

            // Set log_date in YYYYMMDD format (as integer)
            $log->log_date = (int) $loggedAt->format('Ymd');

            // Set log_time in HHMMSS format (as integer)
            $log->log_time = (int) $loggedAt->format('His');
        });
    }

    /**
     * Get the employee that owns this log
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'EMP_EmpID', 'EMP_EmpID');
    }

    /**
     * Get the action type for this log (deprecated - using numeric system now)
     */
    public function actionType(): BelongsTo
    {
        return $this->belongsTo(ActionType::class, 'action_type', 'Type');
    }

    /**
     * Scope for today's logs
     */
    public function scopeToday($query)
    {
        return $query->whereDate('log_date', today());
    }

    /**
     * Scope for specific employee
     */
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('EMP_EmpID', $employeeId);
    }

    /**
     * Scope for specific action type
     */
    public function scopeOfType($query, $actionType)
    {
        return $query->where('action_type', $actionType);
    }

    /**
     * Scope for specific action type (by string)
     */
    public function scopeOfTypeString($query, $actionTypeString)
    {
        $actionTypeMap = [
            'check_in' => 1,
            'check_out' => 2,
            'break_in' => 3,
            'break_out' => 4,
        ];

        $actionTypeId = $actionTypeMap[$actionTypeString] ?? null;

        if ($actionTypeId) {
            return $query->where('action_type', $actionTypeId);
        }

        return $query->whereRaw('1 = 0'); // Return no results if action type not found
    }

    /**
     * Get formatted time for display
     */
    public function getFormattedTimeAttribute(): string
    {
        return $this->logged_at->format('g:i A');
    }

    /**
     * Get formatted date for display
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->logged_at->format('M d, Y');
    }

    /**
     * Get action type label (using numeric system)
     */
    public function getActionLabelAttribute(): string
    {
        $actionLabels = [
            1 => 'Check In',
            2 => 'Check Out',
            3 => 'Break In',
            4 => 'Break Out',
        ];

        return $actionLabels[$this->action_type] ?? 'Unknown Action';
    }

    /**
     * Get action type string
     */
    public function getActionTypeStringAttribute(): string
    {
        $actionStrings = [
            1 => 'check_in',
            2 => 'check_out',
            3 => 'break_in',
            4 => 'break_out',
        ];

        return $actionStrings[$this->action_type] ?? 'unknown';
    }
}
