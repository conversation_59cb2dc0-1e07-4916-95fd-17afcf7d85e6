<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    echo "Checking attendance_logs table structure:" . PHP_EOL;
    
    // Get column information
    $columns = DB::select("
        SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') as IS_IDENTITY
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'attendance_logs'
        ORDER BY ORDINAL_POSITION
    ");
    
    foreach ($columns as $column) {
        echo sprintf(
            "Column: %-15s | Type: %-10s | Nullable: %-3s | Default: %-10s | Identity: %s" . PHP_EOL,
            $column->COLUMN_NAME,
            $column->DATA_TYPE,
            $column->IS_NULLABLE,
            $column->COLUMN_DEFAULT ?? 'NULL',
            $column->IS_IDENTITY ? 'YES' : 'NO'
        );
    }
    
    echo PHP_EOL . "Current max ID in table:" . PHP_EOL;
    $maxId = DB::table('attendance_logs')->max('id');
    echo "Max ID: " . ($maxId ?? 'NULL') . PHP_EOL;
    
    echo PHP_EOL . "Sample records:" . PHP_EOL;
    $records = DB::table('attendance_logs')->orderBy('id', 'desc')->limit(3)->get();
    foreach ($records as $record) {
        echo "ID: {$record->id}, EMP_ID: {$record->EMP_EmpID}, Action: {$record->action_type}, Logged: {$record->logged_at}" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
    echo 'File: ' . $e->getFile() . ':' . $e->getLine() . PHP_EOL;
}
