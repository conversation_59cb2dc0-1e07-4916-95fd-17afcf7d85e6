<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\AttendanceLog;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AttendanceLogSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $employees = Employee::where('EMP_IsActive', 1)->get();
        
        if ($employees->isEmpty()) {
            $this->command->warn('No active employees found. Please run EmployeeSeeder first.');
            return;
        }

        $this->command->info('Creating attendance logs for the last 7 days...');

        // Generate logs for the last 7 days
        for ($day = 6; $day >= 0; $day--) {
            $date = Carbon::now()->subDays($day);
            
            // Skip weekends for more realistic data
            if ($date->isWeekend()) {
                continue;
            }

            $this->command->info("Generating logs for {$date->format('Y-m-d')} ({$date->format('l')})");

            foreach ($employees as $employee) {
                // 90% chance employee comes to work
                if (rand(1, 100) <= 90) {
                    $this->createDailyAttendanceLogs($employee, $date);
                }
            }
        }

        // Create some logs for today
        $this->command->info('Creating some logs for today...');
        $todayEmployees = $employees->random(min(5, $employees->count()));
        
        foreach ($todayEmployees as $employee) {
            $this->createTodayLogs($employee);
        }

        $totalLogs = AttendanceLog::count();
        $this->command->info("✅ Attendance logs created successfully!");
        $this->command->info("📊 Total logs created: {$totalLogs}");
    }

    /**
     * Create daily attendance logs for an employee
     */
    private function createDailyAttendanceLogs(Employee $employee, Carbon $date): void
    {
        $logs = [];
        
        // Check in (8:00 AM ± 30 minutes)
        $checkInTime = $date->copy()->setTime(8, 0)->addMinutes(rand(-30, 30));
        $logs[] = [
            'EMP_EmpID' => $employee->EMP_EmpID,
            'EMP_BioID' => $employee->EMP_BioID,
            'action_type' => 'check_in',
            'logged_at' => $checkInTime,
        ];

        // Break out (12:00 PM ± 15 minutes) - 80% chance
        if (rand(1, 100) <= 80) {
            $breakOutTime = $date->copy()->setTime(12, 0)->addMinutes(rand(-15, 15));
            $logs[] = [
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_BioID' => $employee->EMP_BioID,
                'action_type' => 'break_out',
                'logged_at' => $breakOutTime,
            ];

            // Break in (1:00 PM ± 15 minutes)
            $breakInTime = $breakOutTime->copy()->addHour()->addMinutes(rand(-15, 15));
            $logs[] = [
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_BioID' => $employee->EMP_BioID,
                'action_type' => 'break_in',
                'logged_at' => $breakInTime,
            ];
        }

        // Check out (5:00 PM ± 45 minutes)
        $checkOutTime = $date->copy()->setTime(17, 0)->addMinutes(rand(-45, 45));
        $logs[] = [
            'EMP_EmpID' => $employee->EMP_EmpID,
            'EMP_BioID' => $employee->EMP_BioID,
            'action_type' => 'check_out',
            'logged_at' => $checkOutTime,
        ];

        // Create all logs for this employee for this day
        foreach ($logs as $logData) {
            AttendanceLog::create($logData);
        }
    }

    /**
     * Create some logs for today (partial day)
     */
    private function createTodayLogs(Employee $employee): void
    {
        $now = Carbon::now();
        $logs = [];

        // Only create check-in if it's after 7 AM
        if ($now->hour >= 7) {
            $checkInTime = $now->copy()->setTime(8, 0)->addMinutes(rand(-30, 30));
            
            // Make sure check-in is not in the future
            if ($checkInTime->lte($now)) {
                $logs[] = [
                    'EMP_EmpID' => $employee->EMP_EmpID,
                    'EMP_BioID' => $employee->EMP_BioID,
                    'action_type' => 'check_in',
                    'logged_at' => $checkInTime,
                ];

                // Break out if it's after 11:30 AM
                if ($now->hour >= 11 && $now->minute >= 30) {
                    $breakOutTime = $now->copy()->setTime(12, 0)->addMinutes(rand(-15, 15));
                    
                    if ($breakOutTime->lte($now)) {
                        $logs[] = [
                            'EMP_EmpID' => $employee->EMP_EmpID,
                            'EMP_BioID' => $employee->EMP_BioID,
                            'action_type' => 'break_out',
                            'logged_at' => $breakOutTime,
                        ];

                        // Break in if it's after 12:45 PM
                        if ($now->hour >= 12 && $now->minute >= 45) {
                            $breakInTime = $breakOutTime->copy()->addHour()->addMinutes(rand(-15, 15));
                            
                            if ($breakInTime->lte($now)) {
                                $logs[] = [
                                    'EMP_EmpID' => $employee->EMP_EmpID,
                                    'EMP_BioID' => $employee->EMP_BioID,
                                    'action_type' => 'break_in',
                                    'logged_at' => $breakInTime,
                                ];
                            }
                        }
                    }
                }
            }
        }

        // Create the logs
        foreach ($logs as $logData) {
            AttendanceLog::create($logData);
        }
    }
}
