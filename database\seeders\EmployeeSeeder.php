<?php

namespace Database\Seeders;

use App\Models\Employee;
use Illuminate\Database\Seeder;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $employees = [
            // IT Department
            [
                'EMP_EmpNo' => 'IT001',
                'EMP_FirstName' => '<PERSON>',
                'EMP_LastName' => 'Doe',
                'EMP_MiddleName' => '<PERSON>',
                'EMP_FullName' => '<PERSON>',
                'EMP_Department' => 'IT Department',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'IT002',
                'EMP_FirstName' => 'Sarah',
                'EMP_LastName' => '<PERSON>',
                'EMP_MiddleName' => 'Anne',
                'EMP_FullName' => '<PERSON>',
                'EMP_Department' => 'IT Department',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'IT003',
                'EMP_FirstName' => '<PERSON>',
                'EMP_LastName' => '<PERSON>',
                'EMP_MiddleName' => '<PERSON>',
                'EMP_FullName' => '<PERSON>',
                'EMP_Department' => 'IT Department',
                'EMP_IsActive' => 1,
            ],

            // Human Resources
            [
                'EMP_EmpNo' => 'HR001',
                'EMP_FirstName' => 'Jane',
                'EMP_LastName' => 'Smith',
                'EMP_MiddleName' => 'Elizabeth',
                'EMP_FullName' => 'Jane Elizabeth Smith',
                'EMP_Department' => 'Human Resources',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'HR002',
                'EMP_FirstName' => 'Amanda',
                'EMP_LastName' => 'Rodriguez',
                'EMP_MiddleName' => 'Maria',
                'EMP_FullName' => 'Amanda Maria Rodriguez',
                'EMP_Department' => 'Human Resources',
                'EMP_IsActive' => 1,
            ],

            // Finance
            [
                'EMP_EmpNo' => 'FIN001',
                'EMP_FirstName' => 'Robert',
                'EMP_LastName' => 'Johnson',
                'EMP_MiddleName' => 'William',
                'EMP_FullName' => 'Robert William Johnson',
                'EMP_Department' => 'Finance',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'FIN002',
                'EMP_FirstName' => 'Lisa',
                'EMP_LastName' => 'Thompson',
                'EMP_MiddleName' => 'Marie',
                'EMP_FullName' => 'Lisa Marie Thompson',
                'EMP_Department' => 'Finance',
                'EMP_IsActive' => 1,
            ],

            // Marketing
            [
                'EMP_EmpNo' => 'MKT001',
                'EMP_FirstName' => 'Maria',
                'EMP_LastName' => 'Garcia',
                'EMP_MiddleName' => 'Carmen',
                'EMP_FullName' => 'Maria Carmen Garcia',
                'EMP_Department' => 'Marketing',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'MKT002',
                'EMP_FirstName' => 'James',
                'EMP_LastName' => 'Miller',
                'EMP_MiddleName' => 'Patrick',
                'EMP_FullName' => 'James Patrick Miller',
                'EMP_Department' => 'Marketing',
                'EMP_IsActive' => 1,
            ],

            // Operations
            [
                'EMP_EmpNo' => 'OPS001',
                'EMP_FirstName' => 'David',
                'EMP_LastName' => 'Brown',
                'EMP_MiddleName' => 'James',
                'EMP_FullName' => 'David James Brown',
                'EMP_Department' => 'Operations',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'OPS002',
                'EMP_FirstName' => 'Jennifer',
                'EMP_LastName' => 'Davis',
                'EMP_MiddleName' => 'Lynn',
                'EMP_FullName' => 'Jennifer Lynn Davis',
                'EMP_Department' => 'Operations',
                'EMP_IsActive' => 1,
            ],

            // Sales
            [
                'EMP_EmpNo' => 'SAL001',
                'EMP_FirstName' => 'Christopher',
                'EMP_LastName' => 'Anderson',
                'EMP_MiddleName' => 'Lee',
                'EMP_FullName' => 'Christopher Lee Anderson',
                'EMP_Department' => 'Sales',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'SAL002',
                'EMP_FirstName' => 'Nicole',
                'EMP_LastName' => 'Taylor',
                'EMP_MiddleName' => 'Rose',
                'EMP_FullName' => 'Nicole Rose Taylor',
                'EMP_Department' => 'Sales',
                'EMP_IsActive' => 1,
            ],

            // Some inactive employees for testing
            [
                'EMP_EmpNo' => 'OLD001',
                'EMP_FirstName' => 'Former',
                'EMP_LastName' => 'Employee',
                'EMP_MiddleName' => 'Test',
                'EMP_FullName' => 'Former Test Employee',
                'EMP_Department' => 'IT Department',
                'EMP_IsActive' => 0,
            ],
            [
                'EMP_EmpNo' => 'SUS001',
                'EMP_FirstName' => 'Suspended',
                'EMP_LastName' => 'User',
                'EMP_MiddleName' => 'Test',
                'EMP_FullName' => 'Suspended Test User',
                'EMP_Department' => 'Operations',
                'EMP_IsActive' => 0,
            ],
        ];

        foreach ($employees as $employeeData) {
            $employee = Employee::create($employeeData);
            $this->command->info("Created employee: {$employee->EMP_FullName} with barcode: {$employee->EMP_BioID}");
        }

        $this->command->info('✅ Sample employees created successfully!');
        $this->command->info('📊 Total employees created: ' . count($employees));
        $this->command->info('🏢 Departments: IT, HR, Finance, Marketing, Operations, Sales');
    }
}
