<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\AttendanceLog;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeder for testing specific scenarios.
     */
    public function run(): void
    {
        $this->command->info('🧪 Creating test data for specific scenarios...');

        // Create test employees with predictable barcodes for easy testing
        $testEmployees = [
            [
                'EMP_EmpNo' => 'TEST001',
                'EMP_BioID' => 'EMP12345', // Easy to type for testing
                'EMP_FirstName' => 'Test',
                'EMP_LastName' => 'User',
                'EMP_MiddleName' => 'One',
                'EMP_FullName' => 'Test One User',
                'EMP_Department' => 'Testing',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'TEST002',
                'EMP_BioID' => 'EMP54321', // Easy to type for testing
                'EMP_FirstName' => 'Demo',
                'EMP_LastName' => 'Employee',
                'EMP_MiddleName' => 'Two',
                'EMP_FullName' => 'Demo Two Employee',
                'EMP_Department' => 'Testing',
                'EMP_IsActive' => 1,
            ],
            [
                'EMP_EmpNo' => 'TEST003',
                'EMP_BioID' => 'EMP11111', // Easy to type for testing
                'EMP_FirstName' => 'Quick',
                'EMP_LastName' => 'Test',
                'EMP_MiddleName' => 'Three',
                'EMP_FullName' => 'Quick Three Test',
                'EMP_Department' => 'Testing',
                'EMP_IsActive' => 1,
            ],
        ];

        foreach ($testEmployees as $employeeData) {
            // Check if employee already exists
            $existing = Employee::where('EMP_BioID', $employeeData['EMP_BioID'])->first();
            if (!$existing) {
                $employee = Employee::create($employeeData);
                $this->command->info("✅ Created test employee: {$employee->EMP_FullName} (Barcode: {$employee->EMP_BioID})");
            } else {
                $this->command->info("⚠️  Test employee already exists: {$existing->EMP_FullName} (Barcode: {$existing->EMP_BioID})");
            }
        }

        // Create some specific test scenarios
        $this->createTestScenarios();

        $this->command->info('🎯 Test data created successfully!');
        $this->command->info('📝 You can now test with these barcodes:');
        $this->command->info('   - EMP12345 (Test One User)');
        $this->command->info('   - EMP54321 (Demo Two Employee)');
        $this->command->info('   - EMP11111 (Quick Three Test)');
    }

    /**
     * Create specific test scenarios
     */
    private function createTestScenarios(): void
    {
        $testEmployee = Employee::where('EMP_BioID', 'EMP12345')->first();
        
        if (!$testEmployee) {
            return;
        }

        $this->command->info('Creating test scenarios...');

        // Scenario 1: Employee who checked in today but hasn't checked out
        $this->createScenario1($testEmployee);

        // Scenario 2: Employee with complete day yesterday
        $this->createScenario2($testEmployee);

        // Scenario 3: Employee with irregular pattern
        $this->createScenario3($testEmployee);
    }

    /**
     * Scenario 1: Employee checked in today, ready for next action
     */
    private function createScenario1(Employee $employee): void
    {
        $today = Carbon::today();
        
        // Check if already has logs today
        $existingLogs = AttendanceLog::where('EMP_EmpID', $employee->EMP_EmpID)
            ->whereDate('log_date', $today)
            ->count();

        if ($existingLogs == 0) {
            // Create check-in for this morning
            AttendanceLog::create([
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_BioID' => $employee->EMP_BioID,
                'action_type' => 'check_in',
                'logged_at' => $today->copy()->setTime(8, 30),
            ]);

            $this->command->info("📋 Scenario 1: {$employee->EMP_FullName} checked in today at 8:30 AM");
        }
    }

    /**
     * Scenario 2: Complete day yesterday
     */
    private function createScenario2(Employee $employee): void
    {
        $yesterday = Carbon::yesterday();
        
        // Check if already has logs yesterday
        $existingLogs = AttendanceLog::where('EMP_EmpID', $employee->EMP_EmpID)
            ->whereDate('log_date', $yesterday)
            ->count();

        if ($existingLogs == 0) {
            $logs = [
                [
                    'action_type' => 'check_in',
                    'logged_at' => $yesterday->copy()->setTime(8, 15),
                ],
                [
                    'action_type' => 'break_out',
                    'logged_at' => $yesterday->copy()->setTime(12, 0),
                ],
                [
                    'action_type' => 'break_in',
                    'logged_at' => $yesterday->copy()->setTime(13, 0),
                ],
                [
                    'action_type' => 'check_out',
                    'logged_at' => $yesterday->copy()->setTime(17, 30),
                ],
            ];

            foreach ($logs as $logData) {
                AttendanceLog::create([
                    'EMP_EmpID' => $employee->EMP_EmpID,
                    'EMP_BioID' => $employee->EMP_BioID,
                    'action_type' => $logData['action_type'],
                    'logged_at' => $logData['logged_at'],
                ]);
            }

            $this->command->info("📋 Scenario 2: {$employee->EMP_FullName} had complete day yesterday");
        }
    }

    /**
     * Scenario 3: Irregular pattern (missed break)
     */
    private function createScenario3(Employee $employee): void
    {
        $twoDaysAgo = Carbon::now()->subDays(2);
        
        // Check if already has logs
        $existingLogs = AttendanceLog::where('EMP_EmpID', $employee->EMP_EmpID)
            ->whereDate('log_date', $twoDaysAgo)
            ->count();

        if ($existingLogs == 0) {
            $logs = [
                [
                    'action_type' => 'check_in',
                    'logged_at' => $twoDaysAgo->copy()->setTime(8, 45),
                ],
                [
                    'action_type' => 'check_out',
                    'logged_at' => $twoDaysAgo->copy()->setTime(18, 15),
                ],
            ];

            foreach ($logs as $logData) {
                AttendanceLog::create([
                    'EMP_EmpID' => $employee->EMP_EmpID,
                    'EMP_BioID' => $employee->EMP_BioID,
                    'action_type' => $logData['action_type'],
                    'logged_at' => $logData['logged_at'],
                ]);
            }

            $this->command->info("📋 Scenario 3: {$employee->EMP_FullName} worked without break 2 days ago");
        }
    }
}
