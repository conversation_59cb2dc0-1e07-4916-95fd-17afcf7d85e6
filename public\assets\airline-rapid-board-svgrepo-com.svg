<?xml version="1.0" encoding="utf-8"?><!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg fill="#000000" width="800px" height="800px" viewBox="0 0 32 32" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }
    </style>
  </defs>
  <path d="M18,23H16V21a3.0033,3.0033,0,0,0-3-3H9a3.0033,3.0033,0,0,0-3,3v2H4V21a5.0059,5.0059,0,0,1,5-5h4a5.0059,5.0059,0,0,1,5,5Z" transform="translate(0 0)"/>
  <path d="M11,6A3,3,0,1,1,8,9a3,3,0,0,1,3-3m0-2a5,5,0,1,0,5,5A5,5,0,0,0,11,4Z" transform="translate(0 0)"/>
  <rect x="2" y="26.0001" width="28" height="2"/>
  <polygon points="30 8 28 8 28 6 26 6 26 4 30 4 30 8"/>
  <polygon points="19 4 23 4 23 6 21 6 21 8 19 8 19 4"/>
  <rect x="28" y="13.0001" width="2" height="2"/>
  <rect x="26" y="11.0001" width="2" height="2"/>
  <polygon points="19 11 21 11 21 13 23 13 23 15 19 15 19 11"/>
  <rect id="_Transparent_Rectangle_" data-name="&lt;Transparent Rectangle&gt;" class="cls-1" width="32" height="32"/>
</svg>