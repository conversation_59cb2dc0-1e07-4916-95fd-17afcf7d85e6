import '../css/app.css';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import type { DefineComponent } from 'vue';
import { createApp, h } from 'vue';
import { ZiggyVue } from 'ziggy-js';
import { initializeTheme } from './composables/useAppearance';
import PrimeVue from 'primevue/config';
import ToastService from 'primevue/toastservice';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import Select from 'primevue/select';
import Tooltip from 'primevue/tooltip';
import Toast from 'primevue/toast';

// Import PrimeVue v4 theme and styles
import Aura from '@primeuix/themes/aura';
import 'primeicons/primeicons.css';

// Import SVG as component (assuming you've set up ?component imports in vite.config.ts)


const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => (title ? `${title} - ${appName}` : appName),
    resolve: (name) => resolvePageComponent(
        `./pages/${name}.vue`, 
        import.meta.glob<DefineComponent>('./pages/**/*.vue')
    ),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .use(PrimeVue, {
                theme: {
                    preset: Aura,
                    options: {
                        prefix: 'p',
                        darkModeSelector: '.dark',
                        cssLayer: false
                    }
                }
            })
            .use(ToastService)
            .component('Toast', Toast)               
            .component('InputText', InputText)
            .component('Button', Button)
            .component('Select', Select)
            // Register SVG component globally
        
            .directive('tooltip', Tooltip);

        app.mount(el);
        initializeTheme();
    },
    progress: {
        color: '#4B5563',
    },
});