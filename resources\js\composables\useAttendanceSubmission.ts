// import { ref } from 'vue' // Not needed anymore
import { router } from '@inertiajs/vue3'
import { useToast } from 'primevue/usetoast'
import type { Employee, ActionType } from './useEmployeeScanner'

export function useAttendanceSubmission(
  employee_code: any,
  Emp_Type: any,
  employeeDetails: any,
  isSubmittingRef: any,
  isFromBarcodeScan: any,
  inputStartTime: any,
  lastSubmissionTime: any,
  focusInput: () => void,
  ACTION_LABELS: Record<ActionType, string>
) {
  const toast = useToast()

  const submit = () => {
    console.log('Submit function called')
    console.log('employee_code:', employee_code)
    console.log('Emp_Type:', Emp_Type)
    console.log('isFromBarcodeScan:', isFromBarcodeScan)

    if (!employee_code || !Emp_Type) {
      console.error('Missing required refs:', { employee_code, Emp_Type })
      return
    }

    console.log('Submit function called with:', {
      employee_code: employee_code.value,
      Emp_Type: Emp_Type.value,
      isFromBarcodeScan: isFromBarcodeScan.value
    })
    
    // Only basic validation - no blocking conditions
    if (!employee_code.value || !Emp_Type.value) {
      console.log('Submit validation failed - missing required fields')
      return
    }

    const currentTime = Date.now()
    if (currentTime - lastSubmissionTime.value < 1000) {
      return // Silent return, no toast to avoid spam
    }

    lastSubmissionTime.value = currentTime
    isSubmittingRef.value = true

    console.log('Sending POST request to /scan with data:', {
      employee_code: employee_code.value.trim(),
      Emp_Type: Emp_Type.value,
    })

    router.post('/scan', {
      employee_code: employee_code.value.trim(),
      Emp_Type: Emp_Type.value,
    }, {
      onFinish: () => {
        isSubmittingRef.value = false
      },
      onSuccess: (page) => {
        console.log('Submission successful!', page)
        
        // Show success toast
        if (employeeDetails.value) {
          toast.add({
            severity: 'success',
            summary: 'Success',
            detail: `${employeeDetails.value.EMP_FullName} - ${ACTION_LABELS[Emp_Type.value as ActionType]} recorded successfully`,
            life: 5000
          })
        }
        
        // CRITICAL: Clear employee_code immediately for next scan
        employee_code.value = ''
        isFromBarcodeScan.value = false
        inputStartTime.value = 0
        
        // Always refocus input for next scan
        focusInput()
      },
      onError: (errors) => {
        console.log('Submission failed with errors:', errors)
        
        // Show error toast
        const errorMessage = Object.values(errors).flat().join(', ') || 'An error occurred'
        toast.add({
          severity: 'error',
          summary: 'Error',
          detail: errorMessage,
          life: 4000
        })
        
        // Clear input on error but keep employee details visible
        employee_code.value = ''
        isFromBarcodeScan.value = false
        
        // Focus back to input on error
        focusInput()
      }
    })
  }

  const handleFormSubmit = () => {
    submit()
  }

  return {
    isSubmitting: isSubmittingRef,
    submit,
    handleFormSubmit
  }
}
