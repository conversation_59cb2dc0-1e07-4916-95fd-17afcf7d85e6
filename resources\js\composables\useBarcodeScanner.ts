import { ref, watch, nextTick } from 'vue'
import { router } from '@inertiajs/vue3'
import { useToast } from 'primevue/usetoast'
import type { Employee, ActionType } from './useEmployeeScanner'

export function useBarcodeScanner() {
  const toast = useToast()
  
  // Refs for DOM elements
  const barcodeInput = ref<any>(null)
  
  // Timing tracking
  const lastInputTime = ref(0)
  const inputStartTime = ref(0)
  const lastSubmissionTime = ref(0)
  
  // Scanner state
  const isFromBarcodeScan = ref(false)
  
  // Timers
  let lookupTimeout: NodeJS.Timeout | null = null

  // Focus input helper function
  const focusInput = () => {
    nextTick(() => {
      barcodeInput.value?.$el?.focus()
    })
  }

  // Auto-lookup employee when typing (real-time with debounce)
  const setupEmployeeLookup = (
    employee_code: any, 
    lookupEmployee: (code: string) => void
  ) => {
    watch(employee_code, (newCode: string, oldCode: string) => {
      console.log('Watcher triggered with newCode:', newCode, 'oldCode:', oldCode)
      
      const currentTime = Date.now()
      
      // Clear any existing lookup timeout
      if (lookupTimeout) {
        clearTimeout(lookupTimeout)
      }
      
      // Clean the code by removing newlines and special characters
      const cleanCode = newCode.replace(/[\n\r\t]/g, '').trim()
      console.log('Cleaned code:', cleanCode)
      
      // Validate code format
      if (cleanCode && !/^[A-Za-z0-9]+$/.test(cleanCode)) {
        console.log('Invalid characters in employee code:', cleanCode)
        return
      }
      
      // Track input timing for barcode detection
      if (!oldCode || oldCode.length === 0) {
        inputStartTime.value = currentTime
      }
      lastInputTime.value = currentTime
      
      // Check if this is from a barcode scan
      const hasNewline = newCode.includes('\n')
      const isLongCode = cleanCode.length >= 10
      const inputDuration = currentTime - inputStartTime.value
      const isQuickInput = inputDuration < 200 && cleanCode.length >= 8
      
      isFromBarcodeScan.value = hasNewline || isLongCode || isQuickInput || isFromBarcodeScan.value
      
      // Update the employee_code with cleaned value if different
      if (cleanCode !== newCode) {
        employee_code.value = cleanCode
        return
      }
      
      // Don't clear employee details when code is empty
      if (cleanCode === '') {
        isFromBarcodeScan.value = false
        inputStartTime.value = 0
        return
      }
      
      // Lookup employee with shorter delay for better responsiveness
      if (cleanCode && cleanCode.length >= 3) {
        console.log('Setting up lookup timeout for:', cleanCode)
        const delay = isFromBarcodeScan.value ? 100 : 200
        lookupTimeout = setTimeout(() => {
          const currentCode = employee_code.value.replace(/\n/g, '').trim()
          console.log('Timeout executing, checking if code still matches:', currentCode, 'vs', cleanCode)
          if (currentCode === cleanCode) {
            console.log('Calling lookupEmployee from watcher with:', cleanCode)
            lookupEmployee(cleanCode)
          }
        }, delay)
      }
    })
  }

  const handleInputEvent = (event: Event) => {
    const currentTime = Date.now()
    const value = (event.target as HTMLInputElement).value
    
    console.log('Input event:', { value, length: value.length, currentTime })
    
    // Track timing for barcode detection
    if (value.length === 1) {
      inputStartTime.value = currentTime
      console.log('Input start time set:', inputStartTime.value)
    }
    
    // If we get a string quickly, it's likely a barcode scan
    if (value.length >= 5) {
      const inputDuration = currentTime - inputStartTime.value
      console.log('Input event - length:', value.length, 'duration:', inputDuration)
      
      if (inputDuration < 500) {
        console.log('Detected barcode scan via input timing')
        isFromBarcodeScan.value = true
      }
    }
    
    // Additional pattern-based detection for barcode-like strings
    if (value.length >= 3) {
      const barcodePattern = /^[a-zA-Z0-9]+$/
      if (barcodePattern.test(value)) {
        const inputDuration = currentTime - inputStartTime.value
        if (inputDuration < 800) {
          console.log('Detected barcode scan via pattern matching')
          isFromBarcodeScan.value = true
        }
      }
    }
    
    // Aggressive fallback: if it looks like an employee code
    if (value.length >= 6 && /^emp\d+$/i.test(value)) {
      console.log('Detected barcode scan via employee code pattern (emp + numbers)')
      isFromBarcodeScan.value = true
    }
  }

  const handleEnterKey = (
    employee_code: any,
    employeeDetails: any,
    isLookingUp: any,
    Emp_Type: any,
    lookupEmployee: (code: string) => void,
    handleFormSubmit: () => void
  ) => {
    return (event: KeyboardEvent) => {
      const cleanCode = employee_code.value.replace(/\n/g, '').trim()
      
      // Additional barcode detection
      const currentTime = Date.now()
      if (inputStartTime.value > 0) {
        const totalInputDuration = currentTime - inputStartTime.value
        if (totalInputDuration < 1000 && cleanCode.length >= 3) {
          console.log('Detected barcode scan via Enter key timing - duration:', totalInputDuration)
          isFromBarcodeScan.value = true
        }
      }
      
      if (cleanCode.length >= 3) {
        if (!employeeDetails.value && !isLookingUp.value) {
          lookupEmployee(cleanCode)
        } else {
          handleFormSubmit()
        }
      }
    }
  }

  const handleBlurEvent = (
    employee_code: any,
    employeeDetails: any,
    isLookingUp: any,
    Emp_Type: any,
    lookupEmployee: (code: string) => void,
    submit: () => void
  ) => {
    return (event: FocusEvent) => {
      const currentTime = Date.now()
      const cleanCode = employee_code.value.replace(/\n/g, '').trim()
      
      if (inputStartTime.value > 0 && cleanCode.length >= 3) {
        const totalInputDuration = currentTime - inputStartTime.value
        if (totalInputDuration < 1500) {
          console.log('Detected potential barcode scan via blur event - duration:', totalInputDuration)
          isFromBarcodeScan.value = true
          
          if (Emp_Type.value && cleanCode.length >= 3) {
            if (!employeeDetails.value && !isLookingUp.value) {
              lookupEmployee(cleanCode)
            } else if (employeeDetails.value) {
              setTimeout(() => {
                if (employeeDetails.value && employee_code.value.trim() && Emp_Type.value) {
                  console.log('Auto-submitting via blur detection')
                  submit()
                }
              }, 300)
            }
          }
        }
      }
    }
  }

  const cleanup = () => {
    if (lookupTimeout) {
      clearTimeout(lookupTimeout)
      lookupTimeout = null
    }
  }

  return {
    // Refs
    barcodeInput,
    isFromBarcodeScan,
    
    // Functions
    focusInput,
    setupEmployeeLookup,
    handleInputEvent,
    handleEnterKey,
    handleBlurEvent,
    cleanup,
    
    // State
    lastInputTime,
    inputStartTime,
    lastSubmissionTime
  }
}
