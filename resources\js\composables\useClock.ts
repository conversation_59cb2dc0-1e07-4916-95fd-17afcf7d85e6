import { ref, onMounted, onUnmounted } from 'vue'

export function useClock() {
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]
  const week = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

  const digital_time = ref('')
  const date = ref('')
  const weekday = ref('')
  const ampm = ref('')

  const zeroPadding = (num: number, digit: number) => num.toString().padStart(digit, '0')

  const updateTime = () => {
    const cd = new Date()
    
    let hours = cd.getHours()
    const ampmLocal = hours >= 12 ? 'PM' : 'AM'
    hours = hours % 12 || 12 // Convert to 12-hour format

    // Time format: HH:MM:SS (AM/PM shown separately)
    digital_time.value = `${zeroPadding(hours, 2)}:${zeroPadding(cd.getMinutes(), 2)}:${zeroPadding(cd.getSeconds(), 2)}`
    ampm.value = ampmLocal

    // Date format: Month day, Year Weekday
    date.value = `${months[cd.getMonth()]} ${cd.getDate()}, ${cd.getFullYear()} ${week[cd.getDay()]}`
  }

  let timerId: NodeJS.Timeout | null = null

  onMounted(() => {
    updateTime()
    timerId = setInterval(updateTime, 1000)
  })

  onUnmounted(() => {
    if (timerId) {
      clearInterval(timerId)
    }
  })

  return {
    digital_time,
    date,
    weekday,
    ampm
  }
}
