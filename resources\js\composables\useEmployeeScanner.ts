import { ref, watch, nextTick } from 'vue'
import { router } from '@inertiajs/vue3'
import { useToast } from 'primevue/usetoast'

export interface Employee {
  EMP_BioID: string
  EMP_EmpNo: string
  EMP_FullName: string
  EMP_Department?: string
  EMP_Status: string
  EMP_Photo?: string
  last_action?: {
    action_type: string
    action_label: string
    logged_at: string
  }
}

export type ActionType = 'check_in' | 'check_out' | 'break_in' | 'break_out'
export type ActionTypeId = 1 | 2 | 3 | 4

export function useEmployeeScanner() {
  const toast = useToast()
  
  // State
  const employee_code = ref('')
  const Emp_Type = ref<ActionType | ''>('')
  const employeeDetails = ref<Employee | null>(null)
  const isSubmitting = ref(false)
  const isLookingUp = ref(false)
  const isFromBarcodeScan = ref(false)
  
  // Timing tracking
  const lastInputTime = ref(0)
  const inputStartTime = ref(0)
  const lastSubmissionTime = ref(0)
  
  // Timers
  let lookupTimeout: NodeJS.Timeout | null = null
  const employeeDetailsTimer = ref<NodeJS.Timeout | null>(null)
  const flashMessageTimer = ref<NodeJS.Timeout | null>(null)

  // Constants
  const ACTION_LABELS = {
    check_in: 'Check In',
    check_out: 'Check Out',
    break_in: 'Break In',
    break_out: 'Break Out',
  } as const

  // Action type ID mapping (for database)
  const ACTION_TYPE_IDS = {
    check_in: 1,
    check_out: 2,
    break_in: 3,
    break_out: 4,
  } as const

  // Helper functions
  const getCurrentState = (): ActionType | null => {
    if (!employeeDetails.value) return null
    const lastAction = employeeDetails.value.last_action
    if (!lastAction) return null
    return lastAction.action_type as ActionType
  }

  const getSuggestedActions = (): ActionType[] => {
    if (!employeeDetails.value) return []
    
    const currentState = getCurrentState()
    
    if (!currentState) return ['check_in']
    
    const suggestions: Record<ActionType, ActionType[]> = {
      check_in: ['break_out', 'check_out'],
      break_out: ['break_in'],
      break_in: ['break_out', 'check_out'],
      check_out: ['check_in']
    }
    
    return suggestions[currentState] || []
  }

  const getActionButtonClass = (action: ActionType): string => {
    const baseClasses = 'p-4 rounded-lg border-2 transition-all duration-200 min-h-[120px] flex items-center justify-center cursor-pointer'
    const isSelected = Emp_Type.value === action
    const isSuggested = getSuggestedActions().includes(action)
    
    if (isSelected) {
      return `${baseClasses} bg-blue-500 text-white border-blue-600 shadow-lg ring-2 ring-blue-300`
    }
    
    if (isSuggested) {
      return `${baseClasses} bg-green-50 text-green-800 border-green-400 shadow-md ring-2 ring-green-200 hover:bg-green-100`
    }
    
    const actionStyles = {
      check_in: 'bg-green-50 text-green-800 border-green-400 hover:bg-green-100',
      check_out: 'bg-red-50 text-red-800 border-red-400 hover:bg-red-100',
      break_out: 'bg-yellow-50 text-yellow-800 border-yellow-400 hover:bg-yellow-100',
      break_in: 'bg-orange-50 text-orange-800 border-orange-400 hover:bg-orange-100'
    }
    
    return `${baseClasses} ${actionStyles[action]} hover:shadow-md`
  }

  const getEmployeeStatusClass = (status: string): string => {
    const classes = {
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-gray-100 text-gray-800',
      'suspended': 'bg-red-100 text-red-800'
    }
    return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
  }

  const getActionLabel = (actionType: ActionType): string => {
    return ACTION_LABELS[actionType] || actionType
  }

  const playSuccessSound = () => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)
      
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1)
      
      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2)
      
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.2)
    } catch (error) {
      console.log('Audio not supported or blocked')
    }
  }

  // Cleanup function
  const cleanup = () => {
    if (lookupTimeout) {
      clearTimeout(lookupTimeout)
      lookupTimeout = null
    }

    if (employeeDetailsTimer.value) {
      clearTimeout(employeeDetailsTimer.value)
      employeeDetailsTimer.value = null
    }

    if (flashMessageTimer.value) {
      clearTimeout(flashMessageTimer.value)
      flashMessageTimer.value = null
    }
  }

  return {
    // State
    employee_code,
    Emp_Type,
    employeeDetails,
    isSubmitting,
    isLookingUp,
    isFromBarcodeScan,

    // Computed/Helper functions
    getCurrentState,
    getSuggestedActions,
    getActionButtonClass,
    getEmployeeStatusClass,
    getActionLabel,
    playSuccessSound,
    cleanup,

    // Constants
    ACTION_LABELS,
    ACTION_TYPE_IDS,

    // Timers (for cleanup)
    employeeDetailsTimer,
    flashMessageTimer
  }
}
