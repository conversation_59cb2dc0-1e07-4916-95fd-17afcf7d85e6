<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-800">Attendance History</h1>
          <p class="text-gray-600 mt-1">{{ employee.EMP_FullName }} ({{ employee.EMP_BioID }})</p>
        </div>
        <div class="space-x-3">
          <Button 
            label="View Barcode" 
            icon="pi pi-qrcode" 
            @click="$inertia.visit(route('barcode.show', employee.EMP_EmpID))"
            class="p-button-info"
          />
          <Button 
            label="All Logs" 
            icon="pi pi-calendar" 
            @click="$inertia.visit(route('attendance.logs'))"
            class="p-button-secondary"
          />
        </div>
      </div>

      <!-- Employee Info Card -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid md:grid-cols-3 gap-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-700 mb-3">Employee Details</h3>
            <div class="space-y-2">
              <div>
                <span class="text-sm font-medium text-gray-600">Employee Number:</span>
                <span class="ml-2 text-sm text-gray-800">{{ employee.EMP_EmpNo || 'N/A' }}</span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-600">Department:</span>
                <span class="ml-2 text-sm text-gray-800">{{ employee.EMP_Department || 'N/A' }}</span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-600">Status:</span>
                <span :class="getStatusClass(employee.EMP_IsActive)" class="ml-2 px-2 py-1 rounded-full text-xs font-medium">
                  {{ employee.EMP_IsActive === 1 ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 class="text-lg font-semibold text-gray-700 mb-3">Quick Stats</h3>
            <div class="space-y-2">
              <div>
                <span class="text-sm font-medium text-gray-600">Total Logs:</span>
                <span class="ml-2 text-sm font-bold text-gray-800">{{ logs.total }}</span>
              </div>
              <div>
                <span class="text-sm font-medium text-gray-600">Date Range:</span>
                <span class="ml-2 text-sm text-gray-800">{{ getDateRange() }}</span>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-700 mb-3">Date Filter</h3>
            <div class="space-y-2">
              <div>
                <label class="block text-xs font-medium text-gray-600 mb-1">Start Date</label>
                <InputText
                  v-model="filterForm.start_date"
                  type="date"
                  class="w-full text-sm"
                  @change="applyFilters"
                />
              </div>
              <div>
                <label class="block text-xs font-medium text-gray-600 mb-1">End Date</label>
                <InputText
                  v-model="filterForm.end_date"
                  type="date"
                  class="w-full text-sm"
                  @change="applyFilters"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Logs Timeline -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-800">Attendance Timeline</h2>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="log in logs.data" :key="log.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDate(log.logged_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {{ formatTime(log.logged_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getActionClass(log.action_type)" class="px-3 py-1 rounded-full text-sm font-medium">
                    {{ getActionLabel(log.action_type) }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="logs.links && logs.links.length > 3" class="bg-gray-50 px-6 py-3 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700">
              Showing {{ logs.from }} to {{ logs.to }} of {{ logs.total }} results
            </div>
            <div class="flex space-x-1">
              <Button
                v-for="link in logs.links"
                :key="link.label"
                :label="link.label"
                :disabled="!link.url"
                :class="{ 'p-button-outlined': !link.active }"
                @click="link.url && $inertia.visit(link.url)"
                class="p-button-sm"
                v-html="link.label"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="logs.data.length === 0" class="text-center py-12">
        <i class="pi pi-calendar text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No attendance logs found</h3>
        <p class="text-gray-600 mb-4">This employee has no attendance records for the selected date range.</p>
        <Button 
          label="Clear Date Filter" 
          icon="pi pi-times" 
          @click="clearFilters"
          class="p-button-outlined"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'

const props = defineProps({
  employee: Object,
  logs: Object,
  filters: Object,
})

const filterForm = reactive({
  start_date: props.filters.start_date || '',
  end_date: props.filters.end_date || '',
})

function applyFilters() {
  router.get(route('attendance.employee-logs', props.employee.EMP_EmpID), filterForm, {
    preserveState: true,
    preserveScroll: true,
  })
}

function clearFilters() {
  filterForm.start_date = ''
  filterForm.end_date = ''
  applyFilters()
}

function getStatusClass(isActive) {
  return isActive === 1
    ? 'bg-green-100 text-green-800'
    : 'bg-gray-100 text-gray-800'
}

function getActionClass(actionType) {
  const classes = {
    'check_in': 'bg-green-100 text-green-800',
    'check_out': 'bg-red-100 text-red-800',
    'break_in': 'bg-blue-100 text-blue-800',
    'break_out': 'bg-yellow-100 text-yellow-800'
  }
  return classes[actionType] || 'bg-gray-100 text-gray-800'
}

function getActionLabel(actionType) {
  const labels = {
    'check_in': 'Check In',
    'check_out': 'Check Out',
    'break_in': 'Break In',
    'break_out': 'Break Out'
  }
  return labels[actionType] || actionType
}



function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

function formatTime(dateString) {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })
}

function getDateRange() {
  if (props.logs.data.length === 0) return 'No data'
  
  const dates = props.logs.data.map(log => new Date(log.logged_at))
  const minDate = new Date(Math.min(...dates))
  const maxDate = new Date(Math.max(...dates))
  
  if (minDate.toDateString() === maxDate.toDateString()) {
    return minDate.toLocaleDateString()
  }
  
  return `${minDate.toLocaleDateString()} - ${maxDate.toLocaleDateString()}`
}
</script>
