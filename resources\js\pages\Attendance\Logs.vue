<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Attendance Logs</h1>
        <div class="space-x-3">
          <Button 
            label="Back to Scanner" 
            icon="pi pi-arrow-left" 
            @click="$inertia.visit(route('attendance.index'))"
            class="p-button-success"
          />
          <Button 
            label="Employees" 
            icon="pi pi-users" 
            @click="$inertia.visit(route('employees.index'))"
            class="p-button-secondary"
          />
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4 text-gray-700">Filter Logs</h2>
        <div class="grid md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
            <InputText
              v-model="filterForm.date"
              type="date"
              class="w-full"
              @change="applyFilters"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Employee</label>
            <Select
              v-model="filterForm.EMP_EmpID"
              :options="employeeOptions"
              option-label="label"
              option-value="value"
              placeholder="All Employees"
              class="w-full"
              @change="applyFilters"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Action Type</label>
            <Select
              v-model="filterForm.action_type"
              :options="actionOptions"
              option-label="label"
              option-value="value"
              placeholder="All Actions"
              class="w-full"
              @change="applyFilters"
            />
          </div>
          <div class="flex items-end">
            <Button 
              label="Clear Filters" 
              icon="pi pi-times" 
              @click="clearFilters"
              class="p-button-text"
            />
          </div>
        </div>
      </div>

      <!-- Logs Table -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Time
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="log in logs.data" :key="log.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ log.employee?.EMP_FullName || 'Unknown' }}</div>
                    <div class="text-sm text-gray-500">{{ log.EMP_BioID }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getActionClass(log.action_type?.Type)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ log.action_label }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{{ formatDate(log.logged_at) }}</div>
                  <div class="text-gray-500">{{ formatTime(log.logged_at) }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="logs.links && logs.links.length > 3" class="bg-gray-50 px-6 py-3 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700">
              Showing {{ logs.from }} to {{ logs.to }} of {{ logs.total }} results
            </div>
            <div class="flex space-x-1">
              <Button
                v-for="link in logs.links"
                :key="link.label"
                :label="link.label"
                :disabled="!link.url"
                :class="{ 'p-button-outlined': !link.active }"
                @click="link.url && $inertia.visit(link.url)"
                class="p-button-sm"
                v-html="link.label"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="logs.data.length === 0" class="text-center py-12">
        <i class="pi pi-calendar text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No attendance logs found</h3>
        <p class="text-gray-600 mb-4">Try adjusting your filters or check back later.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'

const props = defineProps({
  logs: Object,
  employees: Array,
  actionTypes: Array,
  filters: Object,
})

const filterForm = reactive({
  date: props.filters.date || new Date().toISOString().split('T')[0],
  EMP_EmpID: props.filters.EMP_EmpID || '',
  action_type: props.filters.action_type || '',
})

const employeeOptions = computed(() => [
  { label: 'All Employees', value: '' },
  ...props.employees.map(emp => ({
    label: emp.EMP_FullName,
    value: emp.EMP_EmpID
  }))
])

const actionOptions = computed(() => [
  { label: 'All Actions', value: '' },
  ...props.actionTypes.map(actionType => ({
    label: actionType.action_label,
    value: actionType.action_type
  }))
])

function applyFilters() {
  router.get(route('attendance.logs'), filterForm, {
    preserveState: true,
    preserveScroll: true,
  })
}

function clearFilters() {
  filterForm.date = new Date().toISOString().split('T')[0]
  filterForm.EMP_EmpID = ''
  filterForm.action_type = ''
  applyFilters()
}

function getActionClass(actionType) {
  const classes = {
    'check_in': 'bg-green-100 text-green-800',
    'check_out': 'bg-red-100 text-red-800',
    'break_in': 'bg-blue-100 text-blue-800',
    'break_out': 'bg-yellow-100 text-yellow-800'
  }
  return classes[actionType] || 'bg-gray-100 text-gray-800'
}

function getActionLabel(actionType) {
  const labels = {
    'check_in': 'Check In',
    'check_out': 'Check Out',
    'break_in': 'Break In',
    'break_out': 'Break Out'
  }
  return labels[actionType] || actionType
}



function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString()
}

function formatTime(dateString) {
  return new Date(dateString).toLocaleTimeString()
}
</script>
