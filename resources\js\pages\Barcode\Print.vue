<template>
  <div class="print-page">
    <!-- Print Header -->
    <div class="print-header">
      <h1>Employee Barcode</h1>
      <p>Time Attendance System</p>
    </div>

    <!-- Employee Card -->
    <div class="employee-card">
      <div class="employee-info">
        <h2>{{ employee.EMP_FullName }}</h2>
        <p><strong>Employee ID:</strong> {{ employee.EMP_EmpNo || 'N/A' }}</p>
        <p><strong>Department:</strong> {{ employee.EMP_Department || 'N/A' }}</p>
        <p><strong>Barcode ID:</strong> {{ employee.EMP_BioID }}</p>
      </div>

      <div class="barcode-container">
        <div v-html="barcode" class="barcode"></div>
        <p class="barcode-text">{{ employee.EMP_BioID }}</p>
      </div>
    </div>

    <!-- Instructions -->
    <div class="instructions">
      <h3>Instructions for Use:</h3>
      <ul>
        <li>Present this barcode to the scanner for attendance recording</li>
        <li>Keep this card in a safe and accessible place</li>
        <li>Report lost or damaged cards to HR immediately</li>
        <li>Do not share your barcode with others</li>
      </ul>
    </div>

    <!-- Print Controls (hidden when printing) -->
    <div class="print-controls no-print">
      <Button 
        label="Print" 
        icon="pi pi-print" 
        @click="printPage"
        class="p-button-success mr-3"
      />
      <Button 
        label="Close" 
        icon="pi pi-times" 
        @click="closePage"
        class="p-button-secondary"
      />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import Button from 'primevue/button'

const props = defineProps({
  employee: Object,
  barcode: String,
})

function printPage() {
  window.print()
}

function closePage() {
  window.close()
}

onMounted(() => {
  // Auto-focus for keyboard navigation
  document.title = `Barcode - ${props.employee.EMP_FullName}`
})
</script>

<style scoped>
.print-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.print-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.print-header h1 {
  font-size: 28px;
  margin: 0 0 10px 0;
  color: #333;
}

.print-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.employee-card {
  border: 2px solid #333;
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 30px;
  background: #f9f9f9;
}

.employee-info {
  margin-bottom: 30px;
}

.employee-info h2 {
  font-size: 24px;
  margin: 0 0 15px 0;
  color: #333;
}

.employee-info p {
  font-size: 14px;
  margin: 5px 0;
  color: #555;
}

.barcode-container {
  text-align: center;
  background: white;
  padding: 20px;
  border-radius: 5px;
  border: 1px solid #ddd;
}

.barcode {
  margin-bottom: 10px;
}

.barcode-text {
  font-family: 'Courier New', monospace;
  font-size: 18px;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.instructions {
  background: #f0f8ff;
  padding: 20px;
  border-radius: 5px;
  border-left: 4px solid #007bff;
  margin-bottom: 30px;
}

.instructions h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.instructions ul {
  margin: 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  color: #555;
}

.print-controls {
  text-align: center;
  padding: 20px;
  border-top: 1px solid #ddd;
}

/* Print-specific styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-page {
    max-width: none;
    margin: 0;
    padding: 0;
  }
  
  .employee-card {
    page-break-inside: avoid;
  }
  
  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
</style>
