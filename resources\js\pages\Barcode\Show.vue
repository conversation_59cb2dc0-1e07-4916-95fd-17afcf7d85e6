<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-4xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Employee Barcode</h1>
        <div class="space-x-3">
          <Button 
            label="Print" 
            icon="pi pi-print" 
            @click="printBarcode"
            class="p-button-success"
          />
          <Button 
            label="Download" 
            icon="pi pi-download" 
            @click="downloadBarcode"
            class="p-button-info"
          />
          <Button 
            label="Back to Employees" 
            icon="pi pi-arrow-left" 
            @click="$inertia.visit(route('employees.index'))"
            class="p-button-secondary"
          />
        </div>
      </div>

      <!-- Employee Info Card -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Employee Information</h2>
            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-600">Full Name</label>
                <p class="text-lg text-gray-800">{{ employee.EMP_FullName }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-600">Employee Number</label>
                <p class="text-gray-800">{{ employee.EMP_EmpNo || 'N/A' }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-600">Department</label>
                <p class="text-gray-800">{{ employee.EMP_Department || 'N/A' }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-600">Status</label>
                <span :class="getStatusClass(employee.EMP_Status)" class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ employee.EMP_Status }}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Barcode Information</h2>
            <div class="space-y-3">
              <div>
                <label class="text-sm font-medium text-gray-600">Barcode ID</label>
                <p class="text-lg font-mono text-gray-800">{{ employee.EMP_BioID }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-600">Barcode Type</label>
                <p class="text-gray-800">CODE128</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Barcode Display -->
      <div class="bg-white rounded-lg shadow-md p-6 text-center">
        <h2 class="text-xl font-semibold mb-6 text-gray-700">Employee Barcode</h2>
        
        <div class="bg-gray-50 p-8 rounded-lg inline-block">
          <div v-html="barcode" class="mb-4"></div>
          <p class="text-lg font-mono text-gray-700">{{ employee.EMP_BioID }}</p>
          <p class="text-sm text-gray-600 mt-2">{{ employee.EMP_FullName }}</p>
        </div>

        <div class="mt-6 text-sm text-gray-600">
          <p>Use this barcode for attendance scanning</p>
          <p>Compatible with standard barcode scanners</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import Button from 'primevue/button'

const props = defineProps({
  employee: Object,
  barcode: String,
})

function getStatusClass(status) {
  const classes = {
    'active': 'bg-green-100 text-green-800',
    'inactive': 'bg-gray-100 text-gray-800',
    'suspended': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function printBarcode() {
  window.open(route('barcode.print', props.employee.EMP_EmpID), '_blank')
}

function downloadBarcode() {
  window.open(route('barcode.download', props.employee.EMP_EmpID), '_blank')
}
</script>
