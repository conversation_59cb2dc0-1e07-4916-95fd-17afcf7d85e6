<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-4xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-800">Add New Employee</h1>
          <p class="text-gray-600 mt-1">Create a new employee record with automatic barcode generation</p>
        </div>
        <Button 
          label="Back to List" 
          icon="pi pi-arrow-left" 
          @click="$inertia.visit(route('employees.index'))"
          class="p-button-secondary"
        />
      </div>

      <!-- Form -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <form @submit.prevent="submit">
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Personal Information -->
            <div class="space-y-4">
              <h2 class="text-lg font-semibold text-gray-700 border-b pb-2">Personal Information</h2>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  First Name <span class="text-red-500">*</span>
                </label>
                <InputText
                  v-model="form.EMP_FirstName"
                  :class="{ 'p-invalid': errors.EMP_FirstName }"
                  class="w-full"
                  placeholder="Enter first name"
                />
                <small v-if="errors.EMP_FirstName" class="text-red-500">{{ errors.EMP_FirstName }}</small>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                <InputText
                  v-model="form.EMP_MiddleName"
                  class="w-full"
                  placeholder="Enter middle name (optional)"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Last Name <span class="text-red-500">*</span>
                </label>
                <InputText
                  v-model="form.EMP_LastName"
                  :class="{ 'p-invalid': errors.EMP_LastName }"
                  class="w-full"
                  placeholder="Enter last name"
                />
                <small v-if="errors.EMP_LastName" class="text-red-500">{{ errors.EMP_LastName }}</small>
              </div>
            </div>

            <!-- Work Information -->
            <div class="space-y-4">
              <h2 class="text-lg font-semibold text-gray-700 border-b pb-2">Work Information</h2>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Employee Number</label>
                <InputText
                  v-model="form.EMP_EmpNo"
                  :class="{ 'p-invalid': errors.EMP_EmpNo }"
                  class="w-full"
                  placeholder="Enter employee number (optional)"
                />
                <small v-if="errors.EMP_EmpNo" class="text-red-500">{{ errors.EMP_EmpNo }}</small>
                <small class="text-gray-500">Leave blank for auto-generation</small>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <Select
                  v-model="form.EMP_Department"
                  :options="departmentOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Select department"
                  class="w-full"
                  editable
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Status <span class="text-red-500">*</span>
                </label>
                <Select
                  v-model="form.EMP_IsActive"
                  :options="statusOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Select status"
                  class="w-full"
                  :class="{ 'p-invalid': errors.EMP_IsActive }"
                />
                <small v-if="errors.EMP_IsActive" class="text-red-500">{{ errors.EMP_IsActive }}</small>
              </div>
            </div>
          </div>

          <!-- Barcode Information -->
          <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 class="text-md font-semibold text-blue-800 mb-2">
              <i class="pi pi-info-circle mr-2"></i>
              Barcode Information
            </h3>
            <p class="text-blue-700 text-sm">
              A unique barcode ID will be automatically generated for this employee. 
              The barcode will be in Code128 format and can be printed immediately after creation.
            </p>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 mt-6 pt-6 border-t">
            <Button 
              type="button"
              label="Cancel" 
              icon="pi pi-times" 
              @click="$inertia.visit(route('employees.index'))"
              class="p-button-text"
            />
            <Button 
              type="submit"
              label="Create Employee" 
              icon="pi pi-check"
              :loading="form.processing"
              class="p-button-success"
            />
          </div>
        </form>
      </div>

      <!-- Preview -->
      <div v-if="form.EMP_FirstName || form.EMP_LastName" class="mt-6 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-lg font-semibold text-gray-700 mb-4">Preview</h2>
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-sm text-gray-600 mb-2">Full Name:</div>
          <div class="text-lg font-medium text-gray-800">
            {{ getFullName() }}
          </div>
          <div class="text-sm text-gray-500 mt-2">
            This is how the employee name will appear in the system
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from 'vue'
import { useForm } from '@inertiajs/vue3'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'

defineProps({
  errors: Object,
})

const form = useForm({
  EMP_EmpNo: '',
  EMP_FirstName: '',
  EMP_LastName: '',
  EMP_MiddleName: '',
  EMP_Department: '',
  EMP_IsActive: 1,
})

const departmentOptions = [
  { label: 'IT Department', value: 'IT Department' },
  { label: 'Human Resources', value: 'Human Resources' },
  { label: 'Finance', value: 'Finance' },
  { label: 'Marketing', value: 'Marketing' },
  { label: 'Operations', value: 'Operations' },
  { label: 'Sales', value: 'Sales' },
  { label: 'Customer Service', value: 'Customer Service' },
  { label: 'Administration', value: 'Administration' },
]

const statusOptions = [
  { label: 'Active', value: 1 },
  { label: 'Inactive', value: 0 },
]

function submit() {
  form.post(route('employees.store'), {
    onSuccess: () => {
      // Form will redirect automatically on success
    },
  })
}

function getFullName() {
  const parts = [
    form.EMP_FirstName,
    form.EMP_MiddleName,
    form.EMP_LastName
  ].filter(part => part && part.trim())
  
  return parts.join(' ') || 'Employee Name'
}
</script>

<style scoped>
.p-invalid {
  border-color: #ef4444;
}
</style>
