<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-7xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Employee Management</h1>
        <div class="space-x-3">
          <Button 
            label="Add Employee" 
            icon="pi pi-plus" 
            @click="$inertia.visit(route('employees.create'))"
            class="p-button-success"
          />
          <Button 
            label="Back to Scanner" 
            icon="pi pi-arrow-left" 
            @click="$inertia.visit(route('attendance.index'))"
            class="p-button-secondary"
          />
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <InputText
              v-model="searchForm.search"
              placeholder="Search by name, ID, or barcode..."
              class="w-full"
              @input="debouncedSearch"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <Select
              v-model="searchForm.status"
              :options="statusOptions"
              option-label="label"
              option-value="value"
              placeholder="All Statuses"
              class="w-full"
              @change="search"
            />
          </div>
          <div class="flex items-end">
            <Button 
              label="Clear Filters" 
              icon="pi pi-times" 
              @click="clearFilters"
              class="p-button-text"
            />
          </div>
        </div>
      </div>

      


      <!-- Employee List -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Barcode ID
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="employee in employees.data" :key="employee.EMP_EmpID" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ employee.EMP_FullName }}</div>
                    <div class="text-sm text-gray-500">{{ employee.EMP_EmpNo || 'No Employee Number' }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="font-mono text-sm text-gray-900">{{ employee.EMP_BioID }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ employee.EMP_Department || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getStatusClass(employee.EMP_IsActive)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ employee.EMP_IsActive === 1 ? 'Active' : 'Inactive' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <Button 
                    icon="pi pi-eye" 
                    @click="$inertia.visit(route('employees.show', employee.EMP_EmpID))"
                    class="p-button-text p-button-sm"
                    v-tooltip="'View Details'"
                  />
                  <Button 
                    icon="pi pi-qrcode" 
                    @click="$inertia.visit(route('barcode.show', employee.EMP_EmpID))"
                    class="p-button-text p-button-sm"
                    v-tooltip="'View Barcode'"
                  />
                  <Button 
                    icon="pi pi-pencil" 
                    @click="$inertia.visit(route('employees.edit', employee.EMP_EmpID))"
                    class="p-button-text p-button-sm"
                    v-tooltip="'Edit Employee'"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="employees.links.length > 3" class="bg-gray-50 px-6 py-3 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-700">
              Showing {{ employees.from }} to {{ employees.to }} of {{ employees.total }} results
            </div>
            <div class="flex space-x-1">
              <Button
                v-for="link in employees.links"
                :key="link.label"
                :label="link.label"
                :disabled="!link.url"
                :class="{ 'p-button-outlined': !link.active }"
                @click="$inertia.visit(link.url)"
                class="p-button-sm"
                v-html="link.label"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="employees.data.length === 0" class="text-center py-12">
        <i class="pi pi-users text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No employees found</h3>
        <p class="text-gray-600 mb-4">Get started by adding your first employee.</p>
        <Button 
          label="Add Employee" 
          icon="pi pi-plus" 
          @click="$inertia.visit(route('employees.create'))"
          class="p-button-success"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Select from 'primevue/select'

const props = defineProps({
  employees: Object,
  filters: Object,
})

const searchForm = reactive({
  search: props.filters.search || '',
  status: props.filters.status || '',
})

const statusOptions = [
  { label: 'All Statuses', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
]

let searchTimeout = null

function debouncedSearch() {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    search()
  }, 500)
}

function search() {
  router.get(route('employees.index'), searchForm, {
    preserveState: true,
    preserveScroll: true,
  })
}

function clearFilters() {
  searchForm.search = ''
  searchForm.status = ''
  search()
}

function getStatusClass(isActive) {
  return isActive === 1
    ? 'bg-green-100 text-green-800'
    : 'bg-gray-100 text-gray-800'
}
</script>
