<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-800">{{ employee.EMP_FullName }}</h1>
          <p class="text-gray-600 mt-1">Employee Details</p>
        </div>
        <div class="space-x-3">
          <Button 
            label="Edit" 
            icon="pi pi-pencil" 
            @click="$inertia.visit(route('employees.edit', employee.EMP_EmpID))"
            class="p-button-info"
          />
          <Button 
            label="View Barcode" 
            icon="pi pi-qrcode" 
            @click="$inertia.visit(route('barcode.show', employee.EMP_EmpID))"
            class="p-button-success"
          />
          <Button 
            label="Back to List" 
            icon="pi pi-arrow-left" 
            @click="$inertia.visit(route('employees.index'))"
            class="p-button-secondary"
          />
        </div>
      </div>

      <!-- Employee Information -->
      <div class="grid lg:grid-cols-3 gap-6 mb-6">
        <!-- Basic Info -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4 text-gray-700">Basic Information</h2>
          <div class="space-y-3">
            <div>
              <label class="text-sm font-medium text-gray-600">Full Name</label>
              <p class="text-lg text-gray-800">{{ employee.EMP_FullName }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Employee Number</label>
              <p class="text-gray-800">{{ employee.EMP_EmpNo || 'Not assigned' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Department</label>
              <p class="text-gray-800">{{ employee.EMP_Department || 'Not assigned' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Status</label>
              <span :class="getStatusClass(employee.EMP_Status)" class="px-2 py-1 rounded-full text-xs font-medium">
                {{ employee.EMP_Status }}
              </span>
            </div>
          </div>
        </div>

        <!-- Barcode Info -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4 text-gray-700">Barcode Information</h2>
          <div class="space-y-3">
            <div>
              <label class="text-sm font-medium text-gray-600">Barcode ID</label>
              <p class="text-lg font-mono text-gray-800">{{ employee.EMP_BioID }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Barcode Type</label>
              <p class="text-gray-800">Code128</p>
            </div>
            <div class="pt-2">
              <Button 
                label="Print Barcode" 
                icon="pi pi-print" 
                @click="printBarcode"
                class="p-button-outlined w-full"
              />
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold mb-4 text-gray-700">Quick Stats</h2>
          <div class="space-y-3">
            <div>
              <label class="text-sm font-medium text-gray-600">Total Logs</label>
              <p class="text-2xl font-bold text-gray-800">{{ recentLogs.length }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Last Activity</label>
              <p class="text-gray-800">{{ getLastActivity() }}</p>
            </div>
            <div class="pt-2">
              <Button 
                label="View All Logs" 
                icon="pi pi-calendar" 
                @click="$inertia.visit(route('attendance.employee-logs', employee.EMP_EmpID))"
                class="p-button-outlined w-full"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-800">Recent Activity</h2>
            <Button 
              label="View All" 
              icon="pi pi-external-link" 
              @click="$inertia.visit(route('attendance.employee-logs', employee.EMP_EmpID))"
              class="p-button-text p-button-sm"
            />
          </div>
        </div>

        <div v-if="recentLogs.length > 0" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Time
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="log in recentLogs" :key="log.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{{ formatDate(log.logged_at) }}</div>
                  <div class="text-gray-500">{{ formatTime(log.logged_at) }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getActionClass(log.action_type)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ getActionLabel(log.action_type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getLogStatusClass(log.status)" class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ log.status }}
                  </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-500">
                  {{ log.notes || '-' }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div v-else class="p-12 text-center">
          <i class="pi pi-calendar text-4xl text-gray-400 mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Recent Activity</h3>
          <p class="text-gray-600">This employee hasn't logged any attendance yet.</p>
        </div>
      </div>

      <!-- Actions -->
      <div class="mt-6 flex justify-end space-x-3">
        <Button 
          label="Delete Employee" 
          icon="pi pi-trash" 
          @click="confirmDelete"
          class="p-button-danger p-button-outlined"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'

const props = defineProps({
  employee: Object,
  recentLogs: Array,
})

function getStatusClass(status) {
  const classes = {
    'active': 'bg-green-100 text-green-800',
    'inactive': 'bg-gray-100 text-gray-800',
    'suspended': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function getActionClass(actionType) {
  const classes = {
    'check_in': 'bg-green-100 text-green-800',
    'check_out': 'bg-red-100 text-red-800',
    'break_in': 'bg-blue-100 text-blue-800',
    'break_out': 'bg-yellow-100 text-yellow-800'
  }
  return classes[actionType] || 'bg-gray-100 text-gray-800'
}

function getActionLabel(actionType) {
  const labels = {
    'check_in': 'Check In',
    'check_out': 'Check Out',
    'break_in': 'Break In',
    'break_out': 'Break Out'
  }
  return labels[actionType] || actionType
}

function getLogStatusClass(status) {
  const classes = {
    'success': 'bg-green-100 text-green-800',
    'duplicate': 'bg-yellow-100 text-yellow-800',
    'error': 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString()
}

function formatTime(dateString) {
  return new Date(dateString).toLocaleTimeString()
}

function getLastActivity() {
  if (props.recentLogs.length === 0) return 'No activity'
  
  const lastLog = props.recentLogs[0]
  const date = new Date(lastLog.logged_at)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return 'Less than an hour ago'
  if (diffInHours < 24) return `${diffInHours} hours ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays} days ago`
}

function printBarcode() {
  window.open(route('barcode.print', props.employee.EMP_EmpID), '_blank')
}

function confirmDelete() {
  if (confirm(`Are you sure you want to delete ${props.employee.EMP_FullName}? This action cannot be undone.`)) {
    router.delete(route('employees.destroy', props.employee.EMP_EmpID))
  }
}
</script>
