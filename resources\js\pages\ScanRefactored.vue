<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-4xl mx-auto">
      <!-- Toast Container -->
      <Toast />
      
      <!-- Digital Clock -->
      <div class="sticky top-0 z-20 mb-4">
        <div class="bg-gray-900 text-white rounded-xl shadow-lg px-6 py-4 flex flex-col items-center">
          <p class="text-2xl md:text-3xl font-semibold">{{ date }}</p>
          <p class="font-mono text-6xl md:text-6xl tracking-widest mt-1">
            {{ weekday }} {{ digital_time }}
            <span :class="ampm === 'AM' ? 'text-blue-400' : 'text-orange-400'" class="ml-2">{{ ampm }}</span>
          </p>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button 
            unstyled 
            @click="setType('check_in')" 
            :class="getActionButtonClass('check_in')">
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Check In</span>
            </div>
          </Button>

          <Button 
            unstyled 
            @click="setType('check_out')" 
            :class="getActionButtonClass('check_out')">
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Check Out</span>
            </div>
          </Button>

          <Button 
            unstyled 
            @click="setType('break_in')" 
            :class="getActionButtonClass('break_in')">
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Break In</span>
            </div>
          </Button>

          <Button 
            unstyled 
            @click="setType('break_out')" 
            :class="getActionButtonClass('break_out')">
            <div class="flex flex-col items-center gap-2">
              <span class="text-lg md:text-4xl font-semibold">Break Out</span>
            </div>
          </Button>
        </div>
      </div>
      
      <!-- Barcode Scanner -->
      <div v-if="Emp_Type" class="bg-white rounded-lg shadow-md p-4 md:p-8 mb-8">
        <h2 class="text-lg md:text-xl font-semibold mb-4 text-gray-800 text-center md:text-left">
          Scan Employee Code for: <span class="text-blue-600">{{ getActionLabel(Emp_Type) }}</span>
        </h2>

        <form @submit.prevent="handleFormSubmitWrapper" class="space-y-4">
          <div class="relative">
            <InputText
              v-model="employee_code"
              ref="barcodeInput"
              placeholder="Scan or enter employee barcode..."
              class="w-full text-lg p-3 pr-12"
              @keydown.enter="handleEnterKeyWrapper"
              @input="handleInputEvent"
              @blur="handleBlurEventWrapper"
              :class="{
                'border-red-300 focus:border-red-500': employee_code && employee_code.length < 3,
                'border-green-300 focus:border-green-500': employee_code && employee_code.length >= 3
              }"
            />
            <i class="pi pi-qrcode absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>

          <div class="flex flex-col sm:flex-row gap-3">
            <Button
              type="button"
              label="Submit"
              icon="pi pi-check"
              :loading="isSubmitting"
              class="p-button-success flex-1 sm:flex-none min-h-[48px]"
              @click="handleFormSubmitWrapper"
            />
            <Button
              type="button"
              label="Clear"
              icon="pi pi-times"
              @click="clearForm"
              class="p-button-secondary flex-1 sm:flex-none min-h-[48px]"
            />
          </div>
        </form>
      </div>
      
      <!-- Employee Details -->
      <div v-if="employeeDetails" 
           class="bg-white rounded-lg shadow-md p-4 md:p-6 mb-6 cursor-pointer hover:shadow-lg transition-shadow"
           @click="focusInput"
           title="Click to focus input for next scan">
        <h2 class="text-lg md:text-xl font-semibold mb-4 text-gray-700 flex items-center justify-center md:justify-start">
          <i class="pi pi-user mr-2"></i>
          Employee Details
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Photo Section -->
          <div class="flex justify-center md:justify-start">
            <div class="w-auto h-auto md:w- aspect-square relative md:h-full rounded-lg overflow-hidden bg-gray-100 border-2 border-gray-200">
              <img v-if="employeeDetails.EMP_Photo" :src="employeeDetails.EMP_Photo" :alt="employeeDetails.EMP_FullName"
                class="w-full h-full object-cover" @error="handleImageError" />
              <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
                <i class="pi pi-user text-3xl md:text-4xl"></i>
              </div>
            </div>
          </div>

          <!-- Employee Info -->
          <div class="space-y-3 text-center md:text-left">
            <div>
              <label class="text-sm font-medium text-gray-600">Full Name</label>
              <p class="text-lg font-semibold text-gray-800">{{ employeeDetails.EMP_FullName }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Employee Number</label>
              <p class="text-gray-800">{{ employeeDetails.EMP_EmpNo || 'Not assigned' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Department</label>
              <p class="text-gray-800">{{ employeeDetails.EMP_Department || 'Not assigned' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-gray-600">Status</label>
              <div class="flex justify-center md:justify-start mt-1">
                <span :class="getEmployeeStatusClass(employeeDetails.EMP_Status)"
                  class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ employeeDetails.EMP_Status }}
                </span>
              </div>
            </div>
          </div>

          <!-- Attendance Status -->
          <div class="space-y-3 text-center md:text-left">
            <div v-if="employeeDetails.last_action">
              <label class="text-sm font-medium text-gray-600">Last Action</label>
              <p class="text-sm text-gray-800">{{ employeeDetails.last_action.action_label }}</p>
              <p class="text-xs text-gray-500">{{ employeeDetails.last_action.logged_at }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Loading State -->
      <div v-if="isLookingUp" class="bg-white rounded-lg shadow-md p-6 mb-6 text-center hidden">
        <div class="flex items-center justify-center space-x-2">
          <i class="pi pi-spin pi-spinner text-blue-500"></i>
          <span class="text-gray-600">Looking up employee...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { usePage } from '@inertiajs/vue3'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import Toast from 'primevue/toast'

// Import our composables
import { useClock } from '@/composables/useClock'
import { useEmployeeScanner } from '@/composables/useEmployeeScanner'
import { useBarcodeScanner } from '@/composables/useBarcodeScanner'
import { useEmployeeLookup } from '@/composables/useEmployeeLookup'
import { useAttendanceSubmission } from '@/composables/useAttendanceSubmission'

// Clock functionality
const { digital_time, date, weekday, ampm } = useClock()

// Employee scanner functionality
const {
  employee_code,
  Emp_Type,
  employeeDetails,
  isSubmitting,
  isLookingUp,
  isFromBarcodeScan,
  getCurrentState,
  getSuggestedActions,
  getActionButtonClass,
  getEmployeeStatusClass,
  getActionLabel,
  playSuccessSound,
  cleanup: cleanupScanner,
  ACTION_LABELS,
  employeeDetailsTimer,
  flashMessageTimer
} = useEmployeeScanner()

// Barcode scanner functionality
const {
  barcodeInput,
  focusInput,
  setupEmployeeLookup,
  handleInputEvent,
  handleEnterKey,
  handleBlurEvent,
  cleanup: cleanupBarcode,
  lastInputTime,
  inputStartTime,
  lastSubmissionTime
} = useBarcodeScanner()

// Employee lookup functionality
const { lookupEmployee } = useEmployeeLookup()

// Attendance submission functionality
const { submit, handleFormSubmit } = useAttendanceSubmission()

// Page and flash messages
const page = usePage()
const flash = computed(() => page.props.flash || {})

// Setup functions and event handlers
const setType = (type) => {
  Emp_Type.value = type
  employee_code.value = ''
  isFromBarcodeScan.value = false
  inputStartTime.value = 0
  focusInput()
}

const clearForm = () => {
  cleanupScanner()
  cleanupBarcode()

  employee_code.value = ''
  Emp_Type.value = ''
  employeeDetails.value = null
  isFromBarcodeScan.value = false
  inputStartTime.value = 0
  lastInputTime.value = 0
}

const handleImageError = (event) => {
  event.target.style.display = 'none'
}

// Setup watchers and event handlers
const setupWatchers = () => {
  // Auto-focus on barcode input when action is selected
  watch(Emp_Type, (newType) => {
    if (newType) {
      focusInput()
    }
  })

  // Setup employee lookup watcher
  setupEmployeeLookup(employee_code, (code) =>
    lookupEmployee(
      code,
      employeeDetails,
      isFromBarcodeScan,
      Emp_Type,
      employee_code,
      () => submit(
        employee_code,
        Emp_Type,
        employeeDetails,
        isFromBarcodeScan,
        inputStartTime,
        lastSubmissionTime,
        focusInput,
        ACTION_LABELS
      )
    )
  )
}

// Create event handlers with proper context
const handleEnterKeyWrapper = handleEnterKey(
  employee_code,
  employeeDetails,
  isLookingUp,
  Emp_Type,
  (code) => lookupEmployee(
    code,
    employeeDetails,
    isFromBarcodeScan,
    Emp_Type,
    employee_code,
    () => submit(
      employee_code,
      Emp_Type,
      employeeDetails,
      isFromBarcodeScan,
      inputStartTime,
      lastSubmissionTime,
      focusInput,
      ACTION_LABELS
    )
  ),
  () => handleFormSubmit(
    employee_code,
    Emp_Type,
    employeeDetails,
    isFromBarcodeScan,
    inputStartTime,
    lastSubmissionTime,
    focusInput,
    ACTION_LABELS
  )
)

const handleBlurEventWrapper = handleBlurEvent(
  employee_code,
  employeeDetails,
  isLookingUp,
  Emp_Type,
  (code) => lookupEmployee(
    code,
    employeeDetails,
    isFromBarcodeScan,
    Emp_Type,
    employee_code,
    () => submit(
      employee_code,
      Emp_Type,
      employeeDetails,
      isFromBarcodeScan,
      inputStartTime,
      lastSubmissionTime,
      focusInput,
      ACTION_LABELS
    )
  ),
  () => submit(
    employee_code,
    Emp_Type,
    employeeDetails,
    isFromBarcodeScan,
    inputStartTime,
    lastSubmissionTime,
    focusInput,
    ACTION_LABELS
  )
)

const handleFormSubmitWrapper = () => {
  handleFormSubmit(
    employee_code,
    Emp_Type,
    employeeDetails,
    isFromBarcodeScan,
    inputStartTime,
    lastSubmissionTime,
    focusInput,
    ACTION_LABELS
  )
}

// Initialize on mount
onMounted(() => {
  setupWatchers()

  // Focus on the page load if there's an action selected
  if (Emp_Type.value) {
    focusInput()
  }
})

// Cleanup on unmount
onUnmounted(() => {
  cleanupScanner()
  cleanupBarcode()
})
