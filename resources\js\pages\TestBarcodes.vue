<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="max-w-6xl mx-auto">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-800">Test Barcodes</h1>
          <p class="text-gray-600 mt-2">Use these barcodes to test your scanner</p>
        </div>
        <div class="space-x-3">
          <Button 
            label="Back to Scanner" 
            icon="pi pi-arrow-left" 
            @click="$inertia.visit(route('attendance.index'))"
            class="p-button-success"
          />
          <Button 
            label="Print All" 
            icon="pi pi-print" 
            @click="printAll"
            class="p-button-info"
          />
        </div>
      </div>

      <!-- Instructions -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h2 class="text-lg font-semibold text-blue-800 mb-3">
          <i class="pi pi-info-circle mr-2"></i>
          How to Test Your Scanner
        </h2>
        <div class="grid md:grid-cols-2 gap-4 text-blue-700">
          <div>
            <h3 class="font-medium mb-2">Scanner Testing:</h3>
            <ul class="space-y-1 text-sm">
              <li>• Print any barcode below</li>
              <li>• Go to the main scanner page</li>
              <li>• Select an action (Check In, etc.)</li>
              <li>• Scan the printed barcode</li>
              <li>• Verify auto-submission works</li>
            </ul>
          </div>
          <div>
            <h3 class="font-medium mb-2">Manual Testing:</h3>
            <ul class="space-y-1 text-sm">
              <li>• Copy any EMP_BioID below</li>
              <li>• Go to the main scanner page</li>
              <li>• Select an action</li>
              <li>• Type the EMP_BioID manually</li>
              <li>• Press Enter or click Submit</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Test Employees Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div 
          v-for="employee in employees" 
          :key="employee.EMP_EmpID"
          class="bg-white rounded-lg shadow-md overflow-hidden"
        >
          <!-- Employee Info -->
          <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">{{ employee.EMP_FullName }}</h3>
            <p class="text-sm text-gray-600">{{ employee.EMP_EmpNo }}</p>
            <p class="text-sm text-gray-600">{{ employee.EMP_Department }}</p>
          </div>

          <!-- Barcode Display -->
          <div class="p-6 text-center bg-gray-50">
            <div class="bg-white p-4 rounded border-2 border-dashed border-gray-300 mb-4">
              <div class="text-xs text-gray-500 mb-2">Barcode Preview</div>
              <div class="font-mono text-lg font-bold text-gray-800 mb-2">
                {{ employee.EMP_BioID }}
              </div>
              <div class="text-xs text-gray-500">
                (Actual barcode will be generated when printed)
              </div>
            </div>

            <!-- Copy Button -->
            <div class="mb-4">
              <Button 
                :label="copiedId === employee.EMP_BioID ? 'Copied!' : 'Copy ID'"
                :icon="copiedId === employee.EMP_BioID ? 'pi pi-check' : 'pi pi-copy'"
                @click="copyToClipboard(employee.EMP_BioID)"
                :class="copiedId === employee.EMP_BioID ? 'p-button-success' : 'p-button-outlined'"
                class="w-full mb-2"
              />
            </div>

            <!-- Action Buttons -->
            <div class="space-y-2">
              <Button 
                label="View Barcode" 
                icon="pi pi-qrcode" 
                @click="$inertia.visit(route('barcode.show', employee.EMP_EmpID))"
                class="p-button-info w-full"
              />
              <Button 
                label="Print Barcode" 
                icon="pi pi-print" 
                @click="printBarcode(employee.EMP_EmpID)"
                class="p-button-secondary w-full"
              />
              <Button 
                label="Test Scan" 
                icon="pi pi-play" 
                @click="testScan(employee.EMP_BioID)"
                class="p-button-success w-full"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="employees.length === 0" class="text-center py-12">
        <i class="pi pi-exclamation-triangle text-6xl text-gray-400 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Test Employees Found</h3>
        <p class="text-gray-600 mb-4">Run the test data seeder to create test employees.</p>
        <div class="bg-gray-100 rounded-lg p-4 max-w-md mx-auto">
          <p class="text-sm font-mono text-gray-700">php artisan setup:test-data</p>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-700">Quick Actions</h2>
        <div class="grid md:grid-cols-3 gap-4">
          <Button 
            label="Setup Test Data" 
            icon="pi pi-database" 
            @click="showSetupCommand"
            class="p-button-outlined"
          />
          <Button 
            label="View All Employees" 
            icon="pi pi-users" 
            @click="$inertia.visit(route('employees.index'))"
            class="p-button-outlined"
          />
          <Button 
            label="Attendance Logs" 
            icon="pi pi-calendar" 
            @click="$inertia.visit(route('attendance.logs'))"
            class="p-button-outlined"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Button from 'primevue/button'

const props = defineProps({
  employees: Array,
})

const copiedId = ref(null)

function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    copiedId.value = text
    setTimeout(() => {
      copiedId.value = null
    }, 2000)
  })
}

function printBarcode(employeeId) {
  window.open(route('barcode.print', employeeId), '_blank')
}

function printAll() {
  props.employees.forEach(employee => {
    setTimeout(() => {
      printBarcode(employee.EMP_EmpID)
    }, 500)
  })
}

function testScan(bioId) {
  // Navigate to scanner with the bio ID pre-filled
  const url = new URL(route('attendance.index'), window.location.origin)
  url.searchParams.set('test_code', bioId)
  window.open(url.toString(), '_blank')
}

function showSetupCommand() {
  alert('Run this command in your terminal:\n\nphp artisan setup:test-data\n\nThis will create test employees and sample data.')
}
</script>
