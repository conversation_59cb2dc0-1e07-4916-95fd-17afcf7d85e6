<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Employee;
use App\Models\AttendanceLog;

try {
    // Find an active employee
    $employee = Employee::where('EMP_IsActive', 1)->first();

    if ($employee) {
        echo 'Testing with employee: ' . $employee->EMP_Name . ' (ID: ' . $employee->EMP_EmpID . ')' . PHP_EOL;
        
        // Create a new attendance log
        $log = AttendanceLog::create([
            'EMP_EmpID' => $employee->EMP_EmpID,
            'EMP_BioID' => $employee->EMP_BioID,
            'action_type' => 1, // Check In
            'logged_at' => now(),
        ]);
        
        echo 'Created attendance log:' . PHP_EOL;
        echo 'ID: ' . $log->id . PHP_EOL;
        echo 'Employee ID: ' . $log->EMP_EmpID . PHP_EOL;
        echo 'BioID: ' . $log->EMP_BioID . PHP_EOL;
        echo 'Action Type: ' . $log->action_type . PHP_EOL;
        echo 'Logged At: ' . $log->logged_at . PHP_EOL;
        echo 'Log Date: ' . $log->log_date . PHP_EOL;
        echo 'Log Time: ' . $log->log_time . PHP_EOL;
        echo 'Action Label: ' . $log->action_label . PHP_EOL;
        
        // Clean up - delete the test record
        $log->delete();
        echo 'Test record deleted successfully.' . PHP_EOL;
    } else {
        echo 'No active employees found. Please run the seeders first.' . PHP_EOL;
    }
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
    echo 'File: ' . $e->getFile() . ':' . $e->getLine() . PHP_EOL;
}
