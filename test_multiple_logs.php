<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Employee;
use App\Models\AttendanceLog;

try {
    // Find an active employee
    $employee = Employee::where('EMP_IsActive', 1)->first();

    if ($employee) {
        echo 'Testing incremental IDs with employee: ' . $employee->EMP_Name . ' (ID: ' . $employee->EMP_EmpID . ')' . PHP_EOL;
        
        $createdIds = [];
        
        // Create 3 attendance logs to test incremental IDs
        for ($i = 1; $i <= 3; $i++) {
            $log = AttendanceLog::create([
                'EMP_EmpID' => $employee->EMP_EmpID,
                'EMP_BioID' => $employee->EMP_BioID,
                'action_type' => $i, // Different action types
                'logged_at' => now()->addSeconds($i), // Slightly different times
            ]);
            
            $createdIds[] = $log->id;
            
            echo "Log {$i}:" . PHP_EOL;
            echo "  ID: {$log->id}" . PHP_EOL;
            echo "  Action Type: {$log->action_type} ({$log->action_label})" . PHP_EOL;
            echo "  Logged At: {$log->logged_at}" . PHP_EOL;
            echo "  Log Date: {$log->log_date}" . PHP_EOL;
            echo "  Log Time: {$log->log_time}" . PHP_EOL;
            echo PHP_EOL;
        }
        
        // Verify IDs are incremental
        echo "ID Verification:" . PHP_EOL;
        for ($i = 1; $i < count($createdIds); $i++) {
            $diff = $createdIds[$i] - $createdIds[$i-1];
            echo "ID {$createdIds[$i-1]} -> {$createdIds[$i]} (difference: {$diff})" . PHP_EOL;
        }
        
        // Clean up - delete the test records
        AttendanceLog::whereIn('id', $createdIds)->delete();
        echo PHP_EOL . "All test records deleted successfully." . PHP_EOL;
        
    } else {
        echo 'No active employees found. Please run the seeders first.' . PHP_EOL;
    }
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
    echo 'File: ' . $e->getFile() . ':' . $e->getLine() . PHP_EOL;
}
