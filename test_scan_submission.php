<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Employee;
use App\Models\AttendanceLog;
use Illuminate\Http\Request;
use App\Http\Controllers\AttendanceController;

echo "=== Testing Scan Submission Flow ===\n";

try {
    // Step 1: Check if we have test employees
    echo "1. Checking for test employees...\n";
    $employee = Employee::where('EMP_IsActive', 1)->first();
    
    if (!$employee) {
        echo "❌ No active employees found. Please run seeders first.\n";
        exit(1);
    }
    
    echo "✅ Found test employee: {$employee->EMP_FullName} (BioID: {$employee->EMP_BioID})\n";
    
    // Step 2: Test direct AttendanceLog creation (like our previous test)
    echo "\n2. Testing direct AttendanceLog creation...\n";
    
    $directLog = AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 1, // check_in
    ]);
    
    echo "✅ Direct log created with ID: {$directLog->id}\n";
    echo "   Action type: {$directLog->action_type}\n";
    echo "   Action label: {$directLog->action_label}\n";
    
    // Clean up
    $directLog->delete();
    echo "✅ Direct log cleaned up\n";
    
    // Step 3: Test the AttendanceController store method
    echo "\n3. Testing AttendanceController::store method...\n";
    
    // Create a mock request like what comes from Scan.vue
    // Pad the BioID to ensure it meets the 6-character minimum requirement
    $employeeCode = str_pad($employee->EMP_BioID, 6, '0', STR_PAD_LEFT);

    $request = new Request([
        'employee_code' => $employeeCode,
        'Emp_Type' => 'check_in'
    ]);
    
    $controller = new AttendanceController();
    
    echo "   Request data: employee_code={$request->employee_code}, Emp_Type={$request->Emp_Type}\n";
    
    // Count logs before
    $logCountBefore = AttendanceLog::count();
    echo "   Attendance logs before: {$logCountBefore}\n";
    
    // Call the store method
    $response = $controller->store($request);
    
    // Count logs after
    $logCountAfter = AttendanceLog::count();
    echo "   Attendance logs after: {$logCountAfter}\n";
    
    if ($logCountAfter > $logCountBefore) {
        echo "✅ AttendanceController::store created a new log!\n";
        
        // Get the latest log
        $latestLog = AttendanceLog::latest('id')->first();
        echo "   Latest log ID: {$latestLog->id}\n";
        echo "   Action type: {$latestLog->action_type}\n";
        echo "   Action label: {$latestLog->action_label}\n";
        echo "   Employee: {$latestLog->EMP_EmpID}\n";
        
        // Clean up
        $latestLog->delete();
        echo "✅ Test log cleaned up\n";
    } else {
        echo "❌ AttendanceController::store did NOT create a new log\n";
        echo "   Response type: " . get_class($response) . "\n";
        
        if (method_exists($response, 'getContent')) {
            echo "   Response content: " . $response->getContent() . "\n";
        }
    }
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "   Stack trace:\n" . $e->getTraceAsString() . "\n";
}
