<?php

echo "=== Testing Web Submission via HTTP ===\n";

// First, get a CSRF token by making a GET request to the scan page
echo "1. Getting CSRF token...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

$response = curl_exec($ch);
curl_close($ch);

// Extract CSRF token from the response
preg_match('/<meta name="csrf-token" content="([^"]+)"/', $response, $matches);
$csrfToken = $matches[1] ?? null;

if ($csrfToken) {
    echo "✅ CSRF token obtained: " . substr($csrfToken, 0, 20) . "...\n";
} else {
    echo "❌ Could not extract CSRF token\n";
    exit(1);
}

// Test data
$employeeCode = '012345'; // Padded to meet 6-character requirement
$actionType = 'check_in';

echo "\n2. Testing with employee_code: $employeeCode, Emp_Type: $actionType\n";

// Create a cURL request to simulate the frontend submission
$ch = curl_init();

curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/scan');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    'employee_code' => $employeeCode,
    'Emp_Type' => $actionType,
    '_token' => $csrfToken,
]));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_VERBOSE, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

echo "\n3. Sending POST request to http://127.0.0.1:8000/scan with CSRF token...\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "HTTP Status Code: $httpCode\n";

if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "Response received:\n";
    echo "--- Response Headers and Body ---\n";
    echo $response;
    echo "\n--- End Response ---\n";
}

echo "\n=== Test Complete ===\n";
