<?php

use App\Models\Employee;
use App\Models\AttendanceLog;

test('attendance log auto-populates date and time fields', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $log = AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
    ]);

    expect($log->logged_at)->not->toBeNull();
    expect($log->log_date)->not->toBeNull();
    expect($log->log_time)->not->toBeNull();
    expect($log->log_date->toDateString())->toBe(today()->toDateString());
});

test('attendance log belongs to employee', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $log = AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
    ]);

    expect($log->employee)->toBeInstanceOf(Employee::class);
    expect($log->employee->EMP_EmpID)->toBe($employee->EMP_EmpID);
});

test('can scope logs for today', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    // Create today's log
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now(),
    ]);

    // Create yesterday's log
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now()->subDay(),
    ]);

    $todayLogs = AttendanceLog::today()->get();
    expect($todayLogs)->toHaveCount(1);
});

test('can scope logs for specific employee', function () {
    $employee1 = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $employee2 = Employee::create([
        'EMP_FirstName' => 'Jane',
        'EMP_LastName' => 'Smith',
        'EMP_IsActive' => 1,
    ]);

    AttendanceLog::create([
        'EMP_EmpID' => $employee1->EMP_EmpID,
        'EMP_BioID' => $employee1->EMP_BioID,
        'action_type' => 'check_in',
    ]);

    AttendanceLog::create([
        'EMP_EmpID' => $employee2->EMP_EmpID,
        'EMP_BioID' => $employee2->EMP_BioID,
        'action_type' => 'check_in',
    ]);

    $employee1Logs = AttendanceLog::forEmployee($employee1->EMP_EmpID)->get();
    expect($employee1Logs)->toHaveCount(1);
    expect($employee1Logs->first()->EMP_EmpID)->toBe($employee1->EMP_EmpID);
});

test('can scope logs by action type', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
    ]);

    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_out',
    ]);

    $checkInLogs = AttendanceLog::ofType('check_in')->get();
    expect($checkInLogs)->toHaveCount(1);
    expect($checkInLogs->first()->action_type)->toBe('check_in');
});

test('provides formatted time and date attributes', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $log = AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => '2024-01-15 14:30:00',
    ]);

    expect($log->formatted_time)->toBeString();
    expect($log->formatted_date)->toBeString();
    expect($log->formatted_time)->toContain('2:30');
    expect($log->formatted_date)->toContain('Jan 15, 2024');
});

test('provides action label attribute', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_Status' => 'active',
    ]);

    $actions = [
        'check_in' => 'Check In',
        'check_out' => 'Check Out',
        'break_in' => 'Break In',
        'break_out' => 'Break Out',
    ];

    foreach ($actions as $actionType => $expectedLabel) {
        $log = AttendanceLog::create([
            'EMP_EmpID' => $employee->EMP_EmpID,
            'EMP_BioID' => $employee->EMP_BioID,
            'action_type' => $actionType,
        ]);

        expect($log->action_label)->toBe($expectedLabel);
    }
});
