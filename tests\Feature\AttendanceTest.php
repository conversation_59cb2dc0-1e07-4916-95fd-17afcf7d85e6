<?php

use App\Models\Employee;
use App\Models\AttendanceLog;

test('attendance scan page loads successfully', function () {
    $response = $this->get('/');
    
    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page->component('Scan'));
});

test('can record attendance with valid employee code', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $response = $this->post('/scan', [
        'employee_code' => $employee->EMP_BioID,
        'Emp_Type' => 'check_in',
    ]);

    $response->assertRedirect('/');
    $response->assertSessionHas('success');

    $this->assertDatabaseHas('attendance_logs', [
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
    ]);
});

test('cannot record attendance with invalid employee code', function () {
    $response = $this->post('/scan', [
        'employee_code' => 'INVALID123',
        'Emp_Type' => 'check_in',
    ]);

    $response->assertRedirect('/');
    $response->assertSessionHas('error');

    $this->assertDatabaseCount('attendance_logs', 0);
});

test('validates required fields', function () {
    $response = $this->post('/scan', []);

    $response->assertSessionHasErrors(['employee_code', 'Emp_Type']);
});

test('validates action type', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $response = $this->post('/scan', [
        'employee_code' => $employee->EMP_BioID,
        'Emp_Type' => 'invalid_action',
    ]);

    $response->assertSessionHasErrors(['Emp_Type']);
});

test('prevents duplicate entries within 5 minutes', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    // First scan
    $this->post('/scan', [
        'employee_code' => $employee->EMP_BioID,
        'Emp_Type' => 'check_in',
    ])->assertSessionHas('success');

    // Immediate duplicate scan
    $response = $this->post('/scan', [
        'employee_code' => $employee->EMP_BioID,
        'Emp_Type' => 'check_in',
    ]);

    $response->assertRedirect('/');
    $response->assertSessionHas('error');
    
    // Should only have one log entry
    $this->assertDatabaseCount('attendance_logs', 1);
});

test('allows same action after 5 minutes', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    // Create a log entry 6 minutes ago
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now()->subMinutes(6),
    ]);

    // Should allow new entry
    $response = $this->post('/scan', [
        'employee_code' => $employee->EMP_BioID,
        'Emp_Type' => 'check_in',
    ]);

    $response->assertRedirect('/');
    $response->assertSessionHas('success');
    
    $this->assertDatabaseCount('attendance_logs', 2);
});

test('can record all action types', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $actions = ['check_in', 'break_out', 'break_in', 'check_out'];

    foreach ($actions as $action) {
        $response = $this->post('/scan', [
            'employee_code' => $employee->EMP_BioID,
            'Emp_Type' => $action,
        ]);

        $response->assertRedirect('/');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('attendance_logs', [
            'EMP_EmpID' => $employee->EMP_EmpID,
            'action_type' => $action,
        ]);

        // Wait to avoid duplicate detection
        sleep(1);
    }
});

test('trims whitespace from employee code', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $response = $this->post('/scan', [
        'employee_code' => '  ' . $employee->EMP_BioID . '  ',
        'Emp_Type' => 'check_in',
    ]);

    $response->assertRedirect('/');
    $response->assertSessionHas('success');

    $this->assertDatabaseHas('attendance_logs', [
        'EMP_BioID' => $employee->EMP_BioID,
    ]);
});
