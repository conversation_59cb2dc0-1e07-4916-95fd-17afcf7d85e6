<?php

use App\Models\Employee;
use App\Models\AttendanceLog;

test('employee can be created with auto-generated bio id', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    expect($employee->EMP_BioID)->not->toBeNull();
    expect($employee->EMP_BioID)->toStartWith('EMP');
    expect($employee->EMP_FullName)->toBe('John  Doe');
});

test('employee bio id is unique', function () {
    $employee1 = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $employee2 = Employee::create([
        'EMP_FirstName' => 'Jane',
        'EMP_LastName' => 'Smith',
        'EMP_IsActive' => 1,
    ]);

    expect($employee1->EMP_BioID)->not->toBe($employee2->EMP_BioID);
});

test('employee can generate barcode', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    $barcode = $employee->generateBarcode();

    expect($barcode)->toBeString();
    expect($barcode)->toContain('svg');
    expect($barcode)->toContain($employee->EMP_BioID);
});

test('employee can have attendance logs', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now(),
    ]);

    expect($employee->attendanceLogs)->toHaveCount(1);
    expect($employee->attendanceLogs->first()->action_type)->toBe('check_in');
});

test('employee can check if currently checked in', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    // Initially not checked in
    expect($employee->isCheckedIn())->toBeFalse();

    // After check in
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now(),
    ]);

    expect($employee->fresh()->isCheckedIn())->toBeTrue();

    // After check out
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_out',
        'logged_at' => now()->addHour(),
    ]);

    expect($employee->fresh()->isCheckedIn())->toBeFalse();
});

test('employee full name is auto-generated', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_MiddleName' => 'Michael',
        'EMP_IsActive' => 1,
    ]);

    expect($employee->EMP_FullName)->toBe('John Michael Doe');
});

test('employee can get todays logs', function () {
    $employee = Employee::create([
        'EMP_FirstName' => 'John',
        'EMP_LastName' => 'Doe',
        'EMP_IsActive' => 1,
    ]);

    // Create today's log
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now(),
    ]);

    // Create yesterday's log
    AttendanceLog::create([
        'EMP_EmpID' => $employee->EMP_EmpID,
        'EMP_BioID' => $employee->EMP_BioID,
        'action_type' => 'check_in',
        'logged_at' => now()->subDay(),
    ]);

    expect($employee->todayLogs)->toHaveCount(1);
});
