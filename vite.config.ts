import vue from '@vitejs/plugin-vue';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig, splitVendorChunkPlugin } from 'vite';
import path from 'path';
import svgLoader from 'vite-svg-loader'; // Added SVG loader

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            ssr: 'resources/js/ssr.ts',
            refresh: true,
        }),
        tailwindcss(),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
        svgLoader({
            svgoConfig: {
                plugins: [
                    {
                        name: 'preset-default',
                        params: {
                            overrides: {
                                removeViewBox: false // Preserve viewBox for proper scaling
                            }
                        }
                    }
                ]
            }
        }),
        // Helps Vite/ Rollup split vendor chunks more effectively
        splitVendorChunkPlugin(),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'resources/js'),
        },
    },
    build: {
        // Raise the warning threshold (KB) to reduce noisy warnings
        chunkSizeWarningLimit: 1024,
        rollupOptions: {
            output: {
                // Explicitly control large vendor chunks
                manualChunks(id) {
                    if (id.includes('node_modules')) {
                        if (id.includes('primevue') || id.includes('@primeuix') || id.includes('primeicons')) {
                            return 'primevue';
                        }
                        if (id.includes('/vue') && !id.includes('primevue')) {
                            return 'vue';
                        }
                        if (id.includes('@inertiajs') || id.includes('ziggy-js')) {
                            return 'inertia';
                        }
                        if (
                            id.includes('@vueuse') ||
                            id.includes('lucide') ||
                            id.includes('reka-ui') ||
                            id.includes('tailwind-merge') ||
                            id.includes('clsx') ||
                            id.includes('class-variance-authority')
                        ) {
                            return 'ui-utils';
                        }
                        // Fallback vendor chunk
                        return 'vendor';
                    }
                },
            },
        },
    },
});